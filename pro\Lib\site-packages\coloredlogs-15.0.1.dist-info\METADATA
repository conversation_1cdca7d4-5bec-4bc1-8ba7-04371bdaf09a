Metadata-Version: 2.1
Name: coloredlogs
Version: 15.0.1
Summary: Colored terminal output for Python's logging module
Home-page: https://coloredlogs.readthedocs.io
Author: <PERSON>
Author-email: <EMAIL>
License: MIT
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Information Technology
Classifier: Intended Audience :: System Administrators
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: MacOS
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX
Classifier: Operating System :: Unix
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Communications
Classifier: Topic :: Scientific/Engineering :: Human Machine Interfaces
Classifier: Topic :: Software Development
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Software Development :: User Interfaces
Classifier: Topic :: System
Classifier: Topic :: System :: Shells
Classifier: Topic :: System :: System Shells
Classifier: Topic :: System :: Console Fonts
Classifier: Topic :: System :: Logging
Classifier: Topic :: System :: Systems Administration
Classifier: Topic :: Terminals
Classifier: Topic :: Utilities
Requires-Python: >=2.7, !=3.0.*, !=3.1.*, !=3.2.*, !=3.3.*, !=3.4.*
Requires-Dist: humanfriendly (>=9.1)
Provides-Extra: cron
Requires-Dist: capturer (>=2.4) ; extra == 'cron'

coloredlogs: Colored terminal output for Python's logging module
================================================================

.. image:: https://travis-ci.org/xolox/python-coloredlogs.svg?branch=master
   :target: https://travis-ci.org/xolox/python-coloredlogs

.. image:: https://coveralls.io/repos/github/xolox/python-coloredlogs/badge.svg?branch=master
   :target: https://coveralls.io/github/xolox/python-coloredlogs?branch=master

The `coloredlogs` package enables colored terminal output for Python's logging_
module. The ColoredFormatter_ class inherits from `logging.Formatter`_ and uses
`ANSI escape sequences`_ to render your logging messages in color. It uses only
standard colors so it should work on any UNIX terminal. It's currently tested
on Python 2.7, 3.5+ and PyPy (2 and 3). On Windows `coloredlogs` automatically
tries to enable native ANSI support (on up-to-date Windows 10 installations)
and falls back on using colorama_ (if installed). Here is a screen shot of the
demo that is printed when the command ``coloredlogs --demo`` is executed:

.. image:: https://coloredlogs.readthedocs.io/en/latest/_images/defaults.png

Note that the screenshot above includes custom logging levels defined by my
verboselogs_ package: if you install both `coloredlogs` and `verboselogs` it
will Just Work (`verboselogs` is of course not required to use
`coloredlogs`).

.. contents::
   :local:

Installation
------------

The `coloredlogs` package is available on PyPI_ which means installation should
be as simple as:

.. code-block:: console

   $ pip install coloredlogs

There's actually a multitude of ways to install Python packages (e.g. the `per
user site-packages directory`_, `virtual environments`_ or just installing
system wide) and I have no intention of getting into that discussion here, so
if this intimidates you then read up on your options before returning to these
instructions 😉.

Optional dependencies
~~~~~~~~~~~~~~~~~~~~~

Native ANSI support on Windows requires an up-to-date Windows 10 installation.
If this is not working for you then consider installing the colorama_ package:

.. code-block:: console

   $ pip install colorama

Once colorama_ is installed it will be used automatically.

Usage
-----

Here's an example of how easy it is to get started:

.. code-block:: python

   import coloredlogs, logging

   # Create a logger object.
   logger = logging.getLogger(__name__)

   # By default the install() function installs a handler on the root logger,
   # this means that log messages from your code and log messages from the
   # libraries that you use will all show up on the terminal.
   coloredlogs.install(level='DEBUG')

   # If you don't want to see log messages from libraries, you can pass a
   # specific logger object to the install() function. In this case only log
   # messages originating from that logger will show up on the terminal.
   coloredlogs.install(level='DEBUG', logger=logger)

   # Some examples.
   logger.debug("this is a debugging message")
   logger.info("this is an informational message")
   logger.warning("this is a warning message")
   logger.error("this is an error message")
   logger.critical("this is a critical message")

Format of log messages
----------------------

The ColoredFormatter_ class supports user defined log formats so you can use
any log format you like. The default log format is as follows::

 %(asctime)s %(hostname)s %(name)s[%(process)d] %(levelname)s %(message)s

This log format results in the following output::

 2015-10-23 03:32:22 peter-macbook coloredlogs.demo[30462] DEBUG message with level 'debug'
 2015-10-23 03:32:23 peter-macbook coloredlogs.demo[30462] VERBOSE message with level 'verbose'
 2015-10-23 03:32:24 peter-macbook coloredlogs.demo[30462] INFO message with level 'info'
 ...

You can customize the log format and styling using environment variables as
well as programmatically, please refer to the `online documentation`_ for
details.

Enabling millisecond precision
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

If you're switching from `logging.basicConfig()`_ to `coloredlogs.install()`_
you may notice that timestamps no longer include milliseconds. This is because
coloredlogs doesn't output milliseconds in timestamps unless you explicitly
tell it to. There are three ways to do that:

1. The easy way is to pass the `milliseconds` argument to `coloredlogs.install()`_::

    coloredlogs.install(milliseconds=True)

   This became supported in `release 7.1`_ (due to `#16`_).

2. Alternatively you can change the log format `to include 'msecs'`_::

    %(asctime)s,%(msecs)03d %(hostname)s %(name)s[%(process)d] %(levelname)s %(message)s

   Here's what the call to `coloredlogs.install()`_ would then look like::

    coloredlogs.install(fmt='%(asctime)s,%(msecs)03d %(hostname)s %(name)s[%(process)d] %(levelname)s %(message)s')

   Customizing the log format also enables you to change the delimiter that
   separates seconds from milliseconds (the comma above). This became possible
   in `release 3.0`_ which added support for user defined log formats.

3. If the use of ``%(msecs)d`` isn't flexible enough you can instead add ``%f``
   to the date/time format, it will be replaced by the value of ``%(msecs)03d``.
   Support for the ``%f`` directive was added to `release 9.3`_ (due to `#45`_).

Custom logging fields
~~~~~~~~~~~~~~~~~~~~~

The following custom log format fields are supported:

- ``%(hostname)s`` provides the hostname of the local system.
- ``%(programname)s`` provides the name of the currently running program.
- ``%(username)s`` provides the username of the currently logged in user.

When `coloredlogs.install()`_ detects that any of these fields are used in the
format string the applicable logging.Filter_ subclasses are automatically
registered to populate the relevant log record fields.

Changing text styles and colors
-------------------------------

The online documentation contains `an example of customizing the text styles and
colors <https://coloredlogs.readthedocs.io/en/latest/api.html#changing-the-colors-styles>`_.

Colored output from cron
------------------------

When `coloredlogs` is used in a cron_ job, the output that's e-mailed to you by
cron won't contain any ANSI escape sequences because `coloredlogs` realizes
that it's not attached to an interactive terminal. If you'd like to have colors
e-mailed to you by cron there are two ways to make it happen:

.. contents::
   :local:

Modifying your crontab
~~~~~~~~~~~~~~~~~~~~~~

Here's an example of a minimal crontab::

    MAILTO="your-email-address@here"
    CONTENT_TYPE="text/html"
    * * * * * root coloredlogs --to-html your-command

The ``coloredlogs`` program is installed when you install the `coloredlogs`
Python package. When you execute ``coloredlogs --to-html your-command`` it runs
``your-command`` under the external program ``script`` (you need to have this
installed). This makes ``your-command`` think that it's attached to an
interactive terminal which means it will output ANSI escape sequences which
will then be converted to HTML by the ``coloredlogs`` program. Yes, this is a
bit convoluted, but it works great :-)

Modifying your Python code
~~~~~~~~~~~~~~~~~~~~~~~~~~

The ColoredCronMailer_ class provides a context manager that automatically
enables HTML output when the ``$CONTENT_TYPE`` variable has been correctly set
in the crontab.

This requires my capturer_ package which you can install using ``pip install
'coloredlogs[cron]'``. The ``[cron]`` extra will pull in capturer_ 2.4 or newer
which is required to capture the output while silencing it - otherwise you'd
get duplicate output in the emails sent by ``cron``.

The context manager can also be used to retroactively silence output that has
already been produced, this can be useful to avoid spammy cron jobs that have
nothing useful to do but still email their output to the system administrator
every few minutes :-).

Contact
-------

The latest version of `coloredlogs` is available on PyPI_ and GitHub_. The
`online documentation`_ is available on Read The Docs and includes a
changelog_. For bug reports please create an issue on GitHub_. If you have
questions, suggestions, etc. feel free to send me an e-mail at
`<EMAIL>`_.

License
-------

This software is licensed under the `MIT license`_.

© 2020 Peter Odding.


.. External references:
.. _#16: https://github.com/xolox/python-coloredlogs/issues/16
.. _#45: https://github.com/xolox/python-coloredlogs/issues/45
.. _ANSI escape sequences: https://en.wikipedia.org/wiki/ANSI_escape_code#Colors
.. _capturer: https://pypi.org/project/capturer
.. _changelog: https://coloredlogs.readthedocs.org/en/latest/changelog.html
.. _colorama: https://pypi.org/project/colorama
.. _ColoredCronMailer: https://coloredlogs.readthedocs.io/en/latest/api.html#coloredlogs.converter.ColoredCronMailer
.. _ColoredFormatter: https://coloredlogs.readthedocs.io/en/latest/api.html#coloredlogs.ColoredFormatter
.. _coloredlogs.install(): https://coloredlogs.readthedocs.io/en/latest/api.html#coloredlogs.install
.. _cron: https://en.wikipedia.org/wiki/Cron
.. _GitHub: https://github.com/xolox/python-coloredlogs
.. _logging.basicConfig(): https://docs.python.org/2/library/logging.html#logging.basicConfig
.. _logging.Filter: https://docs.python.org/3/library/logging.html#filter-objects
.. _logging.Formatter: https://docs.python.org/2/library/logging.html#logging.Formatter
.. _logging: https://docs.python.org/2/library/logging.html
.. _MIT license: https://en.wikipedia.org/wiki/MIT_License
.. _online documentation: https://coloredlogs.readthedocs.io/
.. _per user site-packages directory: https://www.python.org/dev/peps/pep-0370/
.. <EMAIL>: <EMAIL>
.. _PyPI: https://pypi.org/project/coloredlogs
.. _release 3.0: https://coloredlogs.readthedocs.io/en/latest/changelog.html#release-3-0-2015-10-23
.. _release 7.1: https://coloredlogs.readthedocs.io/en/latest/changelog.html#release-7-1-2017-07-15
.. _release 9.3: https://coloredlogs.readthedocs.io/en/latest/changelog.html#release-9-3-2018-04-29
.. _to include 'msecs': https://stackoverflow.com/questions/6290739/python-logging-use-milliseconds-in-time-format
.. _verboselogs: https://pypi.org/project/verboselogs
.. _virtual environments: http://docs.python-guide.org/en/latest/dev/virtualenvs/


