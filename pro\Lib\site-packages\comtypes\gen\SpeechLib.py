from enum import IntFlag

import comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4 as __wrapper_module__
from comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4 import (
    DISPID_SVSInputSentencePosition, SECFIgnoreCase, LONG_PTR,
    SAFT16kHz16BitMono, DISPID_SPEDisplayText, SPEI_SR_BOOKMARK,
    SPDKL_CurrentConfig, DISPID_SPPValue, SVP_13,
    DISPID_SDKDeleteValue, STSF_FlagCreate, SRTReSent,
    SpeechTokenKeyAttributes, SPRS_INACTIVE, SAFTADPCM_11kHzStereo,
    SpeechAddRemoveWord, DISPID_SPRuleParent, SPWT_DISPLAY,
    SVSFNLPMask, Speech_Max_Word_Length, SAFT44kHz16BitMono,
    ISpeechMMSysAudio, SPPS_RESERVED4, DISPID_SPEEngineConfidence,
    SPWORD, SPSUnknown, DISPID_SRCERecognition, SVEAudioLevel,
    SPPHRASE, DISPID_SRCERecognitionForOtherContext,
    DISPID_SMSADeviceId, DISPID_SRState, DISPID_SRRAlternates,
    SpNotifyTranslator, DISPID_SPPConfidence, DISPID_SBSSeek,
    ISpeechMemoryStream, DISPID_SOTsItem, DISPID_SLWsCount,
    DISPID_SRCESoundEnd, SpeechGrammarTagUnlimitedDictation,
    DISPID_SPEActualConfidence, DISPID_SRIsShared, SPAS_STOP,
    SPAR_Low, SPCT_DICTATION, SPTEXTSELECTIONINFO,
    DISPID_SRCEEndStream, ISpeechPhraseAlternates, SGSDisabled,
    ISpeechObjectToken, SpeechAllElements, SpeechTokenValueCLSID,
    DISPID_SRGetPropertyString, SPAS_CLOSED,
    DISPID_SPIAudioStreamPosition, DISPID_SPRsItem, SREPrivate,
    SpeechPropertyResourceUsage, SVEAllEvents, SPEI_RESERVED6,
    SREStreamStart, SPRECORESULTTIMES, SAFT48kHz16BitStereo,
    SAFTExtendedAudioFormat, DISPID_SPIGetText, ISpPhrase, SFTInput,
    SSFMOpenForRead, DISPID_SPERetainedSizeBytes, DISPID_SPCLangId,
    DISPID_SPEDisplayAttributes, ISpeechTextSelectionInformation,
    SPAO_NONE, DISPID_SRCAudioInInterferenceStatus,
    DISPID_SRGCmdLoadFromObject, SP_VISEME_20, SPRST_NUM_STATES,
    SPAR_Medium, SPSERIALIZEDRESULT, DISPID_SGRSTWeight,
    SPEI_SOUND_END, SPSMF_UPS, SRERequestUI, SpeechCategoryVoices,
    GUID, WAVEFORMATEX, SINoSignal, SAFT48kHz8BitMono,
    ISpeechRecoResultDispatch, IEnumSpObjectTokens,
    SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN, SSTTWildcard,
    DISPID_SRCEventInterests, DISPID_SGRsItem, ISpEventSink,
    SPGS_ENABLED, SAFT44kHz8BitStereo, eWORDTYPE_ADDED,
    ISpeechPhraseElement, SPPS_Function, SPSERIALIZEDPHRASE,
    DISPID_SFSClose, SAFT22kHz16BitStereo, SSSPTRelativeToEnd,
    ISpNotifySink, SPEI_VOICE_CHANGE, DISPID_SPAPhraseInfo,
    DISPID_SVStatus, DISPID_SPCIdToPhone,
    DISPID_SVSCurrentStreamNumber, DISPID_SRRSaveToMemory,
    DISPID_SOTDisplayUI, DISPID_SVEStreamStart, DISPID_SVEPhoneme,
    SAFTCCITT_ALaw_8kHzMono, SAFTDefault, SVSFIsXML, _check_version,
    SPWORDLIST, SDTAll, SPINTERFERENCE_LATENCY_WARNING, SP_VISEME_17,
    SECFIgnoreKanaType, ISpeechPhraseElements, DISPID_SOTCDefault,
    SVP_9, SPEI_TTS_BOOKMARK, SAFT8kHz16BitMono, SPEI_PHRASE_START,
    DISPID_SGRSTNextState, SpeechPropertyHighConfidenceThreshold,
    SAFTADPCM_8kHzStereo, SECFNoSpecialChars, DISPID_SRProfile,
    DISPID_SVDisplayUI, eLEXTYPE_PRIVATE10, SECHighConfidence,
    SGSEnabled, SP_VISEME_11, STCInprocServer,
    DISPID_SPPEngineConfidence, SAFTText, DISPID_SADefaultFormat,
    SGLexical, SECLowConfidence, SDTAlternates,
    ISpPhoneticAlphabetSelection, SAFT24kHz16BitStereo,
    SPEI_RESERVED1, SRSEIsSpeaking, SINone, VARIANT,
    ISpeechGrammarRuleState, SDTPronunciation, DISPID_SVVolume,
    SPSHT_Unknown, DISPID_SMSSetData, SpWaveFormatEx, SP_VISEME_8,
    DISPID_SOTs_NewEnum, SDA_One_Trailing_Space, SPPS_Verb,
    DISPID_SRGDictationUnload, SPSHT_NotOverriden, SVP_2,
    Speech_StreamPos_RealTime, DISPID_SRRSpeakAudio, ISpVoice,
    SRTSMLTimeout, SREAudioLevel, eLEXTYPE_PRIVATE11, tagSTATSTG,
    SPSFunction, SAFTCCITT_uLaw_22kHzMono, SpeechGrammarTagWildcard,
    SPEI_REQUEST_UI, SPBO_PAUSE, SVSFParseAutodetect,
    DISPID_SABIBufferSize, SASClosed, DISPID_SLPType,
    SAFTCCITT_ALaw_44kHzMono, SAFT24kHz8BitMono,
    DISPID_SRCEEnginePrivate, SAFTCCITT_uLaw_11kHzMono,
    SAFT24kHz8BitStereo, DISPID_SRCEPropertyNumberChange,
    SRCS_Disabled, SVSFDefault, SPWORDPRONUNCIATIONLIST,
    DISPID_SPEPronunciation, DISPID_SPACommit, DISPID_SPEsItem,
    SPINTERFERENCE_NOSIGNAL, SP_VISEME_15, SVP_5, dispid,
    DISPID_SLRemovePronunciationByPhoneIds, SpPhoneConverter,
    ISpRecognizer, SDTAudio, SAFT12kHz8BitStereo,
    DISPID_SABIMinNotification, SPVPRI_OVER, eLEXTYPE_MORPHOLOGY,
    DISPID_SRRDiscardResultInfo, eLEXTYPE_PRIVATE4,
    SPEI_ACTIVE_CATEGORY_CHANGED, SAFT12kHz8BitMono,
    ISpPhoneConverter, eWORDTYPE_DELETED,
    ISpeechLexiconPronunciations, DISPID_SAFSetWaveFormatEx,
    DISPID_SRRAudio, SpeechDictationTopicSpelling, SASStop,
    SPCS_DISABLED, DISPID_SRGCmdLoadFromMemory, SPSInterjection,
    SRADynamic, SREBookmark, SPAR_High, SREHypothesis,
    DISPID_SLGetWords, ISpPhraseAlt, SAFTNonStandardFormat,
    DISPID_SGRsCommit, ISpLexicon, IInternetSecurityMgrSite,
    SpeechCategoryPhoneConverters, DISPID_SOTGetAttribute,
    SPINTERFERENCE_TOOLOUD, SAFT48kHz16BitMono, SPAUDIOBUFFERINFO,
    DISPID_SPIRetainedSizeBytes, SVPOver, SPCS_ENABLED, ISpStream,
    DISPID_SDKSetStringValue, SpeechMicTraining, eLEXTYPE_PRIVATE19,
    DISPID_SLPsItem, SAFT32kHz8BitStereo, DISPID_SVVoice, DISPMETHOD,
    DISPID_SPEAudioTimeOffset, SPRS_ACTIVE_USER_DELIMITED,
    SGLexicalNoSpecialChars, eLEXTYPE_PRIVATE1, SPEI_VISEME,
    ISpGrammarBuilder, SPEI_SENTENCE_BOUNDARY, SECFDefault,
    DISPID_SPIAudioSizeTime, SPSMF_SRGS_SEMANTICINTERPRETATION_W3C,
    DISPID_SRSetPropertyNumber, SpeechRegistryUserRoot,
    DISPID_SDKSetBinaryValue, SPWF_INPUT,
    DISPID_SGRSAddSpecialTransition, DISPID_SRCCreateGrammar,
    DISPID_SVSPhonemeId, SVSFNLPSpeakPunc, ISpeechRecognizer,
    DISPID_SLPSymbolic, SRTExtendableParse, ISpeechCustomStream,
    DISPID_SVESentenceBoundary, DISPID_SRCPause, SPPS_Unknown,
    SpeechCategoryAudioIn, DISPID_SREmulateRecognition,
    SSFMCreateForWrite, SAFT11kHz8BitStereo, eLEXTYPE_PRIVATE17,
    SVSFParseSsml, SPEI_RECOGNITION, DISPID_SRCRecognizer,
    DISPID_SPANumberOfElementsInResult, SPPS_Interjection,
    DISPID_SVSInputWordPosition, SGRSTTWildcard,
    DISPID_SVEventInterests, DISPIDSPTSI_SelectionLength,
    DISPID_SVAudioOutput, SPEI_ADAPTATION, SVP_16,
    DISPID_SVSRunningState, DISPIDSPTSI_ActiveOffset, SPEI_RESERVED5,
    SLTUser, __MIDL_IWinTypes_0009, DISPID_SBSFormat, SPPS_RESERVED1,
    DISPID_SLWType, DISPID_SOTCSetId, SP_VISEME_14,
    ISpeechRecognizerStatus, SP_VISEME_18, SGPronounciation,
    DISPID_SRSCurrentStreamNumber, SAFTADPCM_22kHzStereo,
    DISPID_SOTRemove, SDTProperty, ISpeechPhraseReplacements,
    SP_VISEME_21, SWTAdded, SRAExport, SPRS_ACTIVE,
    DISPID_SRCESoundStart, SAFT32kHz16BitStereo,
    ISpeechPhraseAlternate, SAFTADPCM_44kHzStereo,
    DISPID_SRRTTickCount, DISPID_SRGRules, UINT_PTR, SPEI_MIN_TTS,
    DISPID_SOTCEnumerateTokens, WSTRING, DISPID_SLPPhoneIds,
    ISpObjectWithToken, DISPID_SPIEnginePrivateData,
    DISPID_SABufferInfo, SGDSActiveWithAutoPause, SITooFast,
    SREInterference, DISPID_SVAlertBoundary, DISPID_SPEAudioSizeBytes,
    SRERecognition, SpSharedRecoContext, SVEVoiceChange,
    DISPID_SRGCmdSetRuleState, SPSHORTCUTPAIRLIST, SPEI_WORD_BOUNDARY,
    SPSMF_SRGS_SAPIPROPERTIES, DISPID_SPRuleEngineConfidence, SVP_12,
    SGRSTTDictation, ISpeechVoiceStatus, SPPS_SuppressWord,
    DISPID_SRCResume, DISPID_SPRuleChildren, helpstring,
    DISPID_SPEAudioSizeTime, SPCT_SUB_DICTATION,
    DISPID_SASNonBlockingIO, SpeechTokenIdUserLexicon,
    DISPID_SRCEHypothesis, DISPID_SRGCmdSetRuleIdState,
    SPPHRASEREPLACEMENT, SAFT12kHz16BitStereo, SPVPRI_ALERT,
    SAFT24kHz16BitMono, DISPID_SLWsItem, SP_VISEME_12,
    ISpeechGrammarRules, SAFT22kHz16BitMono, SAFTADPCM_44kHzMono,
    DISPID_SRGIsPronounceable, SDTRule, DISPID_SASState,
    tagSPPROPERTYINFO, SRSActiveAlways, SPEI_END_SR_STREAM,
    SAFT8kHz16BitStereo, ISpRecoContext2, SRAORetainAudio,
    SAFT32kHz16BitMono, SpeechCategoryRecognizers, SVP_7,
    eLEXTYPE_PRIVATE12, DISPID_SVEEnginePrivate, SPEI_RESERVED2,
    SpUnCompressedLexicon, DISPID_SGRsDynamic, SVSFVoiceMask, SVP_21,
    SRATopLevel, DISPID_SRDisplayUI, SPWP_KNOWN_WORD_PRONOUNCEABLE,
    DISPID_SWFESamplesPerSec, DISPID_SLPs_NewEnum, SPPS_Noun,
    ISpMMSysAudio, SVP_20, SpeechPropertyComplexResponseSpeed,
    ISpeechGrammarRuleStateTransition, DISPID_SVEStreamEnd,
    SP_VISEME_0, SPCT_SLEEP, DISPID_SRRecognizer, SRAONone,
    DISPID_SLGetPronunciations, SpeechRegistryLocalMachineRoot,
    ISpeechPhraseProperties, SPSSuppressWord, DISPID_SOTSetId,
    DISPID_SPIElements, eLEXTYPE_PRIVATE6,
    SSSPTRelativeToCurrentPosition, SAFTGSM610_8kHzMono,
    SpeechTokenKeyUI, SPINTERFERENCE_LATENCY_TRUNCATE_END,
    SAFT8kHz8BitStereo, STSF_LocalAppData, SpMMAudioOut,
    DISPID_SGRAddState,
    DISPID_SRAllowAudioInputFormatChangesOnNextSet,
    DISPID_SVSInputWordLength, SPDKL_LocalMachine, eLEXTYPE_PRIVATE15,
    ISpeechLexiconWord, Speech_Max_Pron_Length,
    eLEXTYPE_USER_SHORTCUT, DISPID_SRStatus, ISpeechLexicon,
    SAFT16kHz8BitMono, DISPID_SRGetRecognizers,
    eLEXTYPE_LETTERTOSOUND, SpMemoryStream, SP_VISEME_5,
    DISPID_SVSLastStreamNumberQueued, _ULARGE_INTEGER, ISpDataKey,
    ISpRecognizer2, DISPID_SPPNumberOfElements, SAFTGSM610_22kHzMono,
    DISPID_SPILanguageId, SPRST_INACTIVE, DISPID_SOTCId,
    DISPID_SGRsCommitAndSave, SpeechAudioVolume, eLEXTYPE_PRIVATE18,
    DISPID_SPRuleConfidence, tagSPTEXTSELECTIONINFO,
    DISPID_SPRNumberOfElements, IStream, SGRSTTEpsilon,
    DISPID_SPELexicalForm, SPAS_RUN, SPINTERFERENCE_TOOSLOW,
    SAFT16kHz8BitStereo, ISpeechVoice, DISPID_SLRemovePronunciation,
    SDTReplacement, SpPhraseInfoBuilder, DISPID_SWFEFormatTag,
    SREFalseRecognition, DISPID_SLAddPronunciationByPhoneIds,
    DISPID_SRCRetainedAudioFormat, DISPID_SGRSTRule,
    DISPID_SRCCmdMaxAlternates, SP_VISEME_10,
    DISPID_SWFEAvgBytesPerSec, DISPID_SPRDisplayAttributes,
    DISPID_SPRulesItem, DISPID_SPPsCount, SPSMF_SAPI_PROPERTIES,
    eLEXTYPE_PRIVATE16, SAFTCCITT_uLaw_44kHzMono, SPAO_RETAIN_AUDIO,
    DISPID_SRCBookmark, ULONG_PTR, DISPID_SLGetGenerationChange,
    SpResourceManager, SRCS_Enabled, SPPHRASEPROPERTY,
    SAFTCCITT_uLaw_8kHzMono, SPVPRI_NORMAL,
    SpeechPropertyLowConfidenceThreshold, ISpeechFileStream,
    DISPID_SPRText, ISpNotifySource,
    SpeechPropertyNormalConfidenceThreshold,
    SAFTCCITT_ALaw_11kHzStereo, SPEI_SOUND_START,
    SAFT16kHz16BitStereo, SAFTCCITT_uLaw_44kHzStereo,
    IInternetSecurityManager, SVEWordBoundary, SPBO_NONE,
    DISPIDSPTSI_ActiveLength, SLOStatic, eLEXTYPE_PRIVATE2, SVP_1,
    SPEI_START_SR_STREAM, DISPID_SAFType, HRESULT, SPEI_UNDEFINED,
    eLEXTYPE_PRIVATE5, ISpeechResourceLoader, SpShortcut,
    SPRECOGNIZERSTATUS, DISPID_SRGSetTextSelection,
    SPFM_OPEN_READWRITE, DISPID_SRGSetWordSequenceData, SpMMAudioEnum,
    SGDSInactive, SAFTCCITT_uLaw_8kHzStereo, SPINTERFERENCE_NONE,
    ISpeechWaveFormatEx, eLEXTYPE_PRIVATE8, IEnumString, VARIANT_BOOL,
    SREPhraseStart, DISPID_SRCERecognizerStateChange,
    DISPID_SASFreeBufferSpace, DISPID_SOTGetDescription, SpFileStream,
    SAFTCCITT_ALaw_8kHzStereo, SP_VISEME_16, DISPID_SRAudioInput,
    DISPID_SGRs_NewEnum, DISPID_SVSLastBookmarkId, SPPS_Noncontent,
    DISPID_SRSCurrentStreamPosition, SGRSTTWord, SPPHRASERULE,
    SRERecoOtherContext, DISPID_SPPBRestorePhraseFromMemory, SPSLMA,
    SPRS_ACTIVE_WITH_AUTO_PAUSE, SAFTCCITT_ALaw_11kHzMono,
    ISpRecoContext, eLEXTYPE_PRIVATE9, DISPID_SASCurrentSeekPosition,
    COMMETHOD, SLTApp, SREPropertyStringChange,
    DISPID_SRRSetTextFeedback, DISPID_SRSetPropertyString,
    DISPID_SGRsFindRule, DISPID_SPPId, eLEXTYPE_PRIVATE14,
    DISPID_SPIReplacements, SpStreamFormatConverter, SRTStandard,
    SSTTDictation, SVPAlert, DISPID_SPIProperties, DISPID_SPPParent,
    DISPID_SRAudioInputStream, STCAll, SAFT44kHz8BitMono,
    DISPID_SRRPhraseInfo, IServiceProvider, SAFT32kHz8BitMono, SVP_14,
    ISpeechGrammarRule, SpNullPhoneConverter, SASRun, SPEI_RESERVED3,
    eLEXTYPE_USER, SREAllEvents, BSTR, DISPID_SPRsCount,
    SpeechVoiceSkipTypeSentence, DISPID_SRGDictationSetState,
    ISpRecoResult, SPEI_FALSE_RECOGNITION, eLEXTYPE_RESERVED6,
    SWTDeleted, DISPID_SPIGetDisplayAttributes, SRESoundEnd, SVP_10,
    SAFT11kHz16BitMono, DISPID_SGRAttributes,
    DISPID_SVAllowAudioOuputFormatChangesOnNextSet, ISpeechPhraseInfo,
    SITooSlow, DISPID_SRCreateRecoContext, SPWF_SRENGINE,
    SPPS_NotOverriden, DISPID_SPPFirstElement, SP_VISEME_3,
    SP_VISEME_6, ISpObjectTokenCategory, DISPID_SGRSTPropertyId,
    DISPID_SRCEPropertyStringChange, DISPID_SAFGuid, SPPS_LMA,
    DISPID_SPEs_NewEnum, DISPID_SOTsCount,
    SWPUnknownWordUnpronounceable, SSFMOpenReadWrite,
    DISPID_SPPs_NewEnum, DISPID_SDKGetStringValue,
    SpeechTokenKeyFiles, ISpeechRecoResult, ISpeechRecoResultTimes,
    SPPS_Modifier, SP_VISEME_2, SASPause, SRSEDone,
    SpeechGrammarTagDictation, SVF_None, SREStreamEnd,
    SPEI_INTERFERENCE, SPWT_PRONUNCIATION, SDKLLocalMachine,
    _FILETIME, SRTAutopause, DISPID_SGRSTs_NewEnum,
    DISPID_SOTMatchesAttributes, DISPID_SVSpeakStream,
    SpSharedRecognizer, ISpXMLRecoResult, SREPropertyNumChange,
    DISPID_SMSGetData, SPLO_STATIC, SINoise, SPINTERFERENCE_TOOQUIET,
    SPFM_CREATE, SWPKnownWordPronounceable, DISPID_SPEsCount,
    IUnknown, DISPID_SRGRecoContext, DISPID_SOTCreateInstance,
    SPEVENT, DISPID_SRCEAdaptation, DISPID_SPAStartElementInResult,
    DISPID_SRCEAudioLevel, CoClass, DISPID_SRRTimes,
    DISPID_SVGetAudioInputs, SPEI_TTS_AUDIO_LEVEL, SAFT12kHz16BitMono,
    SVP_19, SP_VISEME_9, STSF_AppData, SpeechCategoryAppLexicons,
    DISPID_SLGenerationId, DISPID_SVGetVoices,
    SDA_Consume_Leading_Spaces, SpCompressedLexicon, SVEPhoneme,
    SP_VISEME_19, DISPID_SRRTOffsetFromStart, STCRemoteServer,
    eLEXTYPE_RESERVED4, SAFTCCITT_ALaw_22kHzMono,
    DISPID_SVSInputSentenceLength, DISPID_SRCVoicePurgeEvent,
    DISPID_SGRId, SVEEndInputStream, SpeechCategoryRecoProfiles,
    ISpeechAudioBufferInfo, SP_VISEME_1, ISpeechPhraseReplacement,
    SPEI_SR_AUDIO_LEVEL, SECNormalConfidence, SPCT_SUB_COMMAND,
    SPSNoun, DISPID_SASetState, ISpeechRecoResult2, SPSHT_EMAIL,
    SAFTCCITT_ALaw_22kHzStereo, DISPID_SPRuleNumberOfElements,
    SVSFParseSapi, SPINTERFERENCE_NOISE, DISPID_SRCRequestedUIType,
    eLEXTYPE_RESERVED9, DISPID_SRCEStartStream, DISPID_SWFEChannels,
    DISPID_SRCRetainedAudio, ISpeechRecoContext, DISPID_SRGId,
    DISPID_SRCERequestUI, SPPS_RESERVED3, SPWORDPRONUNCIATION,
    DISPID_SMSAMMHandle, DISPID_SGRClear, SSFMCreate, ISpShortcut,
    DISPID_SPRuleFirstElement, SpPhoneticAlphabetConverter,
    DISPID_SASCurrentDevicePosition, DISPID_SOTCGetDataKey,
    DISPID_SAFGetWaveFormatEx, SDKLDefaultLocation, SPPS_RESERVED2,
    SpeechCategoryAudioOut, SDA_No_Trailing_Space, SPEI_HYPOTHESIS,
    ISpStreamFormat, DISPID_SPIEngineId, SP_VISEME_7,
    SPEVENTSOURCEINFO, SAFT22kHz8BitStereo, SPFM_NUM_MODES,
    SPSNotOverriden, DISPID_SWFEBlockAlign,
    SpeechPropertyResponseSpeed, DISPID_SVResume, SVSFIsNotXML,
    SVEPrivate, ISpRecoGrammar, STCInprocHandler, DISPID_SGRsAdd,
    ISpeechAudioStatus, SAFTCCITT_ALaw_44kHzStereo, SDTLexicalForm,
    SSTTTextBuffer, eLEXTYPE_PRIVATE13, SPEI_SR_PRIVATE, SRARoot,
    SECFEmulateResult, ISpNotifyTranslator, ISpProperties,
    DISPID_SRSSupportedLanguages, DISPID_SPRFirstElement,
    DISPID_SGRSTsItem, ISpResourceManager, SVP_15, eLEXTYPE_APP,
    SpObjectToken, SpTextSelectionInformation, DISPID_SPPName,
    SVSFPersistXML, DISPID_SDKDeleteKey, SFTSREngine,
    DISPID_SDKSetLongValue, DISPID_SPCPhoneToId, DISPID_SRCVoice,
    ISpObjectToken, SRESoundStart, DISPID_SPARecoResult,
    ISpeechObjectTokens, DISPID_SVIsUISupported,
    DISPID_SRCEPhraseStart, DISPID_SGRSTType, SPRULE, SPSHT_OTHER,
    DISPID_SGRAddResource, DISPID_SGRInitialState, SRAImport, SVP_0,
    SpLexicon, ISequentialStream, DISPID_SDKGetBinaryValue,
    SpCustomStream, eLEXTYPE_RESERVED8, SAFT48kHz8BitStereo,
    SPGS_EXCLUSIVE, DISPID_SPERetainedStreamOffset,
    DISPID_SGRSAddWordTransition, ISpeechPhraseRules,
    DISPID_SLPPartOfSpeech, SAFTGSM610_11kHzMono, DISPID_SPAsCount,
    DISPID_SVSLastBookmark, SRSInactive, DISPID_SVSkip,
    _RemotableHandle, _ISpeechRecoContextEvents, SVEViseme,
    DISPID_SVEVoiceChange, SAFT8kHz8BitMono, SLODynamic,
    DISPID_SLPsCount, DISPID_SVSLastResult, DISPID_SVRate,
    SVSFParseMask, DISPID_SRRGetXMLErrorInfo, ISpEventSource,
    DISPID_SPIStartTime, Library, SpInprocRecognizer,
    Speech_Default_Weight, SPSModifier, DISPIDSPTSI_SelectionOffset,
    SVF_Emphasis, SVP_3, typelib_path, DISPID_SGRSAddRuleTransition,
    DISPID_SPIAudioSizeBytes, SAFTNoAssignedFormat,
    ISpPhoneticAlphabetConverter, DISPID_SGRName, SECFIgnoreWidth,
    ISpeechPhraseProperty, DISPID_SGRSRule, SpeechUserTraining,
    DISPID_SPPChildren, DISPID_SRGCmdLoadFromProprietaryGrammar,
    DISPID_SAVolume, SPEI_RECO_OTHER_CONTEXT, DISPID_SRCEInterference,
    DISPID_SOTCategory, SAFT11kHz8BitMono,
    DISPID_SRCCreateResultFromMemory, SPAR_Unknown,
    SPRST_INACTIVE_WITH_PURGE, SPBO_TIME_UNITS,
    DISPID_SOTGetStorageFileName, SPWP_UNKNOWN_WORD_PRONOUNCEABLE,
    eLEXTYPE_PRIVATE20, SpStream, SPEI_RECO_STATE_CHANGE,
    DISPID_SPRulesCount, SpInProcRecoContext, SPFM_OPEN_READONLY,
    DISPID_SDKGetlongValue, SAFTTrueSpeech_8kHz1BitMono,
    SRADefaultToActive, DISPID_SLWPronunciations, ISpRecognizer3,
    SGRSTTTextBuffer, SPEI_TTS_PRIVATE, SVP_6, DISPID_SRRGetXMLResult,
    DISPID_SGRSTPropertyValue, SPDKL_CurrentUser,
    SAFT44kHz16BitStereo, SPEI_START_INPUT_STREAM, SRTEmulated,
    SVESentenceBoundary, ISpStreamFormatConverter,
    DISPID_SRGCmdLoadFromFile, SVPNormal, SAFTGSM610_44kHzMono,
    DISPID_SRIsUISupported, DISPID_SRRTLength, SPRECOCONTEXTSTATUS,
    DISPID_SPRuleId, DISPID_SFSOpen, DISPID_SRGDictationLoad,
    DISPID_SVSpeakCompleteEvent, SPLO_DYNAMIC, ISpRecoGrammar2,
    Speech_StreamPos_Asap, SpeechAudioProperties,
    eLEXTYPE_VENDORLEXICON, SDKLCurrentUser, SPPHRASEELEMENT,
    ISpeechAudio, SPRST_ACTIVE_ALWAYS, SPBO_AHEAD,
    DISPID_SPRs_NewEnum, SP_VISEME_13, ISpeechLexiconPronunciation,
    ISpeechAudioFormat, DISPID_SPAs_NewEnum, DISPID_SGRSTransitions,
    ISpeechPhraseInfoBuilder, SBONone, SVSFUnusedFlags,
    _ISpeechVoiceEvents, SPEI_PROPERTY_NUM_CHANGE, DISPID_SVSpeak,
    DISPID_SDKEnumKeys, SPAUDIOSTATUS, SVP_8, DISPID_SAEventHandle,
    SP_VISEME_4, DISPID_SPRules_NewEnum, SVEStartInputStream,
    ISpeechObjectTokenCategory, SpeechEngineProperties,
    DISPID_SGRSTsCount, SVEBookmark, SVF_Stressed,
    __MIDL___MIDL_itf_sapi_0000_0020_0002, SITooQuiet,
    SAFTCCITT_uLaw_22kHzStereo, DISPID_SRCEBookmark,
    SpeechRecoProfileProperties, DISPID_SRRRecoContext, SPVOICESTATUS,
    DISPID_SRGState, DISPID_SRGCmdLoadFromResource,
    DISPID_SRSAudioStatus, DISPID_SRSClsidEngine, SGDSActive,
    SPWT_LEXICAL_NO_SPECIAL_CHARS, DISPID_SLWWord,
    DISPID_SPISaveToMemory, SAFT11kHz16BitStereo, DISPID_SPIGrammarId,
    ISpeechDataKey, DISPID_SGRSTText, SAFT22kHz8BitMono,
    SpAudioFormat, _lcid, DISPID_SLPLangId, SpVoice, DISPID_SVEWord,
    SPFM_CREATE_ALWAYS, SPEI_MAX_TTS, STCLocalServer,
    SAFTADPCM_22kHzMono, DISPID_SRCEFalseRecognition, SVP_17,
    SPEI_MAX_SR, SPSVerb, eLEXTYPE_RESERVED10,
    DISPID_SPERequiredConfidence, SPSEMANTICERRORINFO,
    SPXRO_Alternates_SML, SPWT_LEXICAL, SPEI_SR_RETAINEDAUDIO,
    SWPUnknownWordPronounceable, DISPID_SPAsItem,
    DISPID_SRRTStreamTime, SPPROPERTYINFO, DISPID_SRCState,
    eLEXTYPE_PRIVATE7, DISPID_SOTDataKey, SPRST_ACTIVE,
    SAFTCCITT_uLaw_11kHzStereo, DISPID_SRSNumberOfActiveRules,
    ISpeechGrammarRuleStateTransitions, SVSFlagsAsync, SDTDisplayText,
    ISpeechPhoneConverter, _LARGE_INTEGER, DISPID_SVEBookmark,
    SpeechAudioFormatGUIDWave, DISPID_SOTId, SDKLCurrentConfig,
    SGSExclusive, SGDSActiveUserDelimited, ISpeechBaseStream,
    SDA_Two_Trailing_Spaces, DISPID_SLAddPronunciation,
    DISPID_SVSyncronousSpeakTimeout, SpeechAudioFormatGUIDText,
    DISPID_SWFEBitsPerSample, DISPID_SPRuleName,
    DISPID_SVGetAudioOutputs, ISpeechPhraseRule,
    DISPID_SVWaitUntilDone, DISPID_SLWs_NewEnum, SGRSTTRule,
    DISPID_SVSVisemeId, SpeechPropertyAdaptationOn, SVSFIsFilename,
    SpeechVoiceCategoryTTSRate, DISPID_SRGCommit, DISPID_SVPriority,
    DISPID_SAStatus, SPEI_END_INPUT_STREAM, DISPID_SVGetProfiles,
    SPSHORTCUTPAIR, ISpSerializeState, DISPID_SLWLangId,
    ISpeechXMLRecoResult, ISpAudio, SVP_18, DISPID_SRGReset,
    ISpRecoCategory, eLEXTYPE_PRIVATE3, DISPID_SDKEnumValues,
    SPEI_PHONEME, DISPID_SDKOpenKey, SVSFPurgeBeforeSpeak,
    SRAInterpreter, SITooLoud, SPAS_PAUSE, DISPID_SVPause,
    SPEI_PROPERTY_STRING_CHANGE, DISPID_SOTIsUISupported,
    SREAdaptation, SAFTADPCM_11kHzMono, DISPID_SABIEventBias, SVP_4,
    SAFTADPCM_8kHzMono, SREStateChange, DISPID_SWFEExtraData,
    DISPID_SPEAudioStreamOffset, SPDKL_DefaultLocation,
    DISPID_SBSRead, DISPID_SCSBaseStream, DISPID_SRRAudioFormat,
    SPCT_COMMAND, __MIDL___MIDL_itf_sapi_0000_0020_0001,
    DISPID_SPIRule, SPEI_MIN_SR, eLEXTYPE_RESERVED7, SRSActive,
    SpObjectTokenCategory, wireHWND, DISPID_SRCSetAdaptationData,
    DISPID_SVEAudioLevel, DISPID_SVAudioOutputStream,
    SPINTERFERENCE_TOOFAST, DISPID_SGRsCount,
    DISPID_SGRSTPropertyName,
    DISPID_SRAllowVoiceFormatMatchingOnNextSet,
    DISPID_SABufferNotifySize, DISPID_SRGetFormat, DISPID_SVEViseme,
    SVP_11, STSF_CommonAppData, SPSMF_SRGS_SEMANTICINTERPRETATION_MS,
    SRSInactiveWithPurge, ISpeechRecoGrammar, SPGS_DISABLED,
    SGDisplay, DISPID_SPPsItem, DISPID_SRGetPropertyNumber,
    DISPID_SMSALineId, DISPID_SOTRemoveStorageFileName,
    SSSPTRelativeToStart, SBOPause, SpMMAudioIn, SPBINARYGRAMMAR,
    SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE, DISPID_SBSWrite, SPXRO_SML,
    DISPID_SDKCreateKey, ISpeechLexiconWords
)


class SpeechRuleAttributes(IntFlag):
    SRATopLevel = 1
    SRADefaultToActive = 2
    SRAExport = 4
    SRAImport = 8
    SRAInterpreter = 16
    SRADynamic = 32
    SRARoot = 64


class SPXMLRESULTOPTIONS(IntFlag):
    SPXRO_SML = 0
    SPXRO_Alternates_SML = 1


class DISPID_SpeechRecoResult2(IntFlag):
    DISPID_SRRSetTextFeedback = 12


class SPWORDTYPE(IntFlag):
    eWORDTYPE_ADDED = 1
    eWORDTYPE_DELETED = 2


class SpeechVoiceSpeakFlags(IntFlag):
    SVSFDefault = 0
    SVSFlagsAsync = 1
    SVSFPurgeBeforeSpeak = 2
    SVSFIsFilename = 4
    SVSFIsXML = 8
    SVSFIsNotXML = 16
    SVSFPersistXML = 32
    SVSFNLPSpeakPunc = 64
    SVSFParseSapi = 128
    SVSFParseSsml = 256
    SVSFParseAutodetect = 0
    SVSFNLPMask = 64
    SVSFParseMask = 384
    SVSFVoiceMask = 511
    SVSFUnusedFlags = -512


class SpeechDiscardType(IntFlag):
    SDTProperty = 1
    SDTReplacement = 2
    SDTRule = 4
    SDTDisplayText = 8
    SDTLexicalForm = 16
    SDTPronunciation = 32
    SDTAudio = 64
    SDTAlternates = 128
    SDTAll = 255


class SPBOOKMARKOPTIONS(IntFlag):
    SPBO_NONE = 0
    SPBO_PAUSE = 1
    SPBO_AHEAD = 2
    SPBO_TIME_UNITS = 4


class SPSHORTCUTTYPE(IntFlag):
    SPSHT_NotOverriden = -1
    SPSHT_Unknown = 0
    SPSHT_EMAIL = 4096
    SPSHT_OTHER = 8192
    SPPS_RESERVED1 = 12288
    SPPS_RESERVED2 = 16384
    SPPS_RESERVED3 = 20480
    SPPS_RESERVED4 = 61440


class SpeechDisplayAttributes(IntFlag):
    SDA_No_Trailing_Space = 0
    SDA_One_Trailing_Space = 2
    SDA_Two_Trailing_Spaces = 4
    SDA_Consume_Leading_Spaces = 8


class SpeechGrammarWordType(IntFlag):
    SGDisplay = 0
    SGLexical = 1
    SGPronounciation = 2
    SGLexicalNoSpecialChars = 3


class SpeechSpecialTransitionType(IntFlag):
    SSTTWildcard = 1
    SSTTDictation = 2
    SSTTTextBuffer = 3


class SpeechRunState(IntFlag):
    SRSEDone = 1
    SRSEIsSpeaking = 2


class SPDATAKEYLOCATION(IntFlag):
    SPDKL_DefaultLocation = 0
    SPDKL_CurrentUser = 1
    SPDKL_LocalMachine = 2
    SPDKL_CurrentConfig = 5


class SPCONTEXTSTATE(IntFlag):
    SPCS_DISABLED = 0
    SPCS_ENABLED = 1


class SPRECOSTATE(IntFlag):
    SPRST_INACTIVE = 0
    SPRST_ACTIVE = 1
    SPRST_ACTIVE_ALWAYS = 2
    SPRST_INACTIVE_WITH_PURGE = 3
    SPRST_NUM_STATES = 4


class SpeechEngineConfidence(IntFlag):
    SECLowConfidence = -1
    SECNormalConfidence = 0
    SECHighConfidence = 1


class _SPAUDIOSTATE(IntFlag):
    SPAS_CLOSED = 0
    SPAS_STOP = 1
    SPAS_PAUSE = 2
    SPAS_RUN = 3


class SpeechGrammarRuleStateTransitionType(IntFlag):
    SGRSTTEpsilon = 0
    SGRSTTWord = 1
    SGRSTTRule = 2
    SGRSTTDictation = 3
    SGRSTTWildcard = 4
    SGRSTTTextBuffer = 5


class SPWAVEFORMATTYPE(IntFlag):
    SPWF_INPUT = 0
    SPWF_SRENGINE = 1


class SPADAPTATIONRELEVANCE(IntFlag):
    SPAR_Unknown = 0
    SPAR_Low = 1
    SPAR_Medium = 2
    SPAR_High = 3


class SpeechBookmarkOptions(IntFlag):
    SBONone = 0
    SBOPause = 1


class SPCATEGORYTYPE(IntFlag):
    SPCT_COMMAND = 0
    SPCT_DICTATION = 1
    SPCT_SLEEP = 2
    SPCT_SUB_COMMAND = 3
    SPCT_SUB_DICTATION = 4


class DISPID_SpeechPhraseBuilder(IntFlag):
    DISPID_SPPBRestorePhraseFromMemory = 1


class SpeechFormatType(IntFlag):
    SFTInput = 0
    SFTSREngine = 1


class DISPID_SpeechPhraseAlternate(IntFlag):
    DISPID_SPARecoResult = 1
    DISPID_SPAStartElementInResult = 2
    DISPID_SPANumberOfElementsInResult = 3
    DISPID_SPAPhraseInfo = 4
    DISPID_SPACommit = 5


class SpeechVoiceEvents(IntFlag):
    SVEStartInputStream = 2
    SVEEndInputStream = 4
    SVEVoiceChange = 8
    SVEBookmark = 16
    SVEWordBoundary = 32
    SVEPhoneme = 64
    SVESentenceBoundary = 128
    SVEViseme = 256
    SVEAudioLevel = 512
    SVEPrivate = 32768
    SVEAllEvents = 33790


class DISPID_SpeechPhraseAlternates(IntFlag):
    DISPID_SPAsCount = 1
    DISPID_SPAsItem = 0
    DISPID_SPAs_NewEnum = -4


class DISPID_SpeechPhraseInfo(IntFlag):
    DISPID_SPILanguageId = 1
    DISPID_SPIGrammarId = 2
    DISPID_SPIStartTime = 3
    DISPID_SPIAudioStreamPosition = 4
    DISPID_SPIAudioSizeBytes = 5
    DISPID_SPIRetainedSizeBytes = 6
    DISPID_SPIAudioSizeTime = 7
    DISPID_SPIRule = 8
    DISPID_SPIProperties = 9
    DISPID_SPIElements = 10
    DISPID_SPIReplacements = 11
    DISPID_SPIEngineId = 12
    DISPID_SPIEnginePrivateData = 13
    DISPID_SPISaveToMemory = 14
    DISPID_SPIGetText = 15
    DISPID_SPIGetDisplayAttributes = 16


class SpeechVoicePriority(IntFlag):
    SVPNormal = 0
    SVPAlert = 1
    SVPOver = 2


class SPPARTOFSPEECH(IntFlag):
    SPPS_NotOverriden = -1
    SPPS_Unknown = 0
    SPPS_Noun = 4096
    SPPS_Verb = 8192
    SPPS_Modifier = 12288
    SPPS_Function = 16384
    SPPS_Interjection = 20480
    SPPS_Noncontent = 24576
    SPPS_LMA = 28672
    SPPS_SuppressWord = 61440


class DISPID_SpeechPhraseElement(IntFlag):
    DISPID_SPEAudioTimeOffset = 1
    DISPID_SPEAudioSizeTime = 2
    DISPID_SPEAudioStreamOffset = 3
    DISPID_SPEAudioSizeBytes = 4
    DISPID_SPERetainedStreamOffset = 5
    DISPID_SPERetainedSizeBytes = 6
    DISPID_SPEDisplayText = 7
    DISPID_SPELexicalForm = 8
    DISPID_SPEPronunciation = 9
    DISPID_SPEDisplayAttributes = 10
    DISPID_SPERequiredConfidence = 11
    DISPID_SPEActualConfidence = 12
    DISPID_SPEEngineConfidence = 13


class SpeechRecognitionType(IntFlag):
    SRTStandard = 0
    SRTAutopause = 1
    SRTEmulated = 2
    SRTSMLTimeout = 4
    SRTExtendableParse = 8
    SRTReSent = 16


class SPLOADOPTIONS(IntFlag):
    SPLO_STATIC = 0
    SPLO_DYNAMIC = 1


class SPLEXICONTYPE(IntFlag):
    eLEXTYPE_USER = 1
    eLEXTYPE_APP = 2
    eLEXTYPE_VENDORLEXICON = 4
    eLEXTYPE_LETTERTOSOUND = 8
    eLEXTYPE_MORPHOLOGY = 16
    eLEXTYPE_RESERVED4 = 32
    eLEXTYPE_USER_SHORTCUT = 64
    eLEXTYPE_RESERVED6 = 128
    eLEXTYPE_RESERVED7 = 256
    eLEXTYPE_RESERVED8 = 512
    eLEXTYPE_RESERVED9 = 1024
    eLEXTYPE_RESERVED10 = 2048
    eLEXTYPE_PRIVATE1 = 4096
    eLEXTYPE_PRIVATE2 = 8192
    eLEXTYPE_PRIVATE3 = 16384
    eLEXTYPE_PRIVATE4 = 32768
    eLEXTYPE_PRIVATE5 = 65536
    eLEXTYPE_PRIVATE6 = 131072
    eLEXTYPE_PRIVATE7 = 262144
    eLEXTYPE_PRIVATE8 = 524288
    eLEXTYPE_PRIVATE9 = 1048576
    eLEXTYPE_PRIVATE10 = 2097152
    eLEXTYPE_PRIVATE11 = 4194304
    eLEXTYPE_PRIVATE12 = 8388608
    eLEXTYPE_PRIVATE13 = 16777216
    eLEXTYPE_PRIVATE14 = 33554432
    eLEXTYPE_PRIVATE15 = 67108864
    eLEXTYPE_PRIVATE16 = 134217728
    eLEXTYPE_PRIVATE17 = 268435456
    eLEXTYPE_PRIVATE18 = 536870912
    eLEXTYPE_PRIVATE19 = 1073741824
    eLEXTYPE_PRIVATE20 = -2147483648


class DISPID_SpeechPhraseElements(IntFlag):
    DISPID_SPEsCount = 1
    DISPID_SPEsItem = 0
    DISPID_SPEs_NewEnum = -4


class SpeechLexiconType(IntFlag):
    SLTUser = 1
    SLTApp = 2


class SpeechPartOfSpeech(IntFlag):
    SPSNotOverriden = -1
    SPSUnknown = 0
    SPSNoun = 4096
    SPSVerb = 8192
    SPSModifier = 12288
    SPSFunction = 16384
    SPSInterjection = 20480
    SPSLMA = 28672
    SPSSuppressWord = 61440


class DISPID_SpeechPhraseReplacement(IntFlag):
    DISPID_SPRDisplayAttributes = 1
    DISPID_SPRText = 2
    DISPID_SPRFirstElement = 3
    DISPID_SPRNumberOfElements = 4


class SpeechWordType(IntFlag):
    SWTAdded = 1
    SWTDeleted = 2


class DISPID_SpeechPhraseReplacements(IntFlag):
    DISPID_SPRsCount = 1
    DISPID_SPRsItem = 0
    DISPID_SPRs_NewEnum = -4


class DISPID_SpeechPhraseProperty(IntFlag):
    DISPID_SPPName = 1
    DISPID_SPPId = 2
    DISPID_SPPValue = 3
    DISPID_SPPFirstElement = 4
    DISPID_SPPNumberOfElements = 5
    DISPID_SPPEngineConfidence = 6
    DISPID_SPPConfidence = 7
    DISPID_SPPParent = 8
    DISPID_SPPChildren = 9


class SpeechLoadOption(IntFlag):
    SLOStatic = 0
    SLODynamic = 1


class DISPID_SpeechPhraseProperties(IntFlag):
    DISPID_SPPsCount = 1
    DISPID_SPPsItem = 0
    DISPID_SPPs_NewEnum = -4


class SpeechRuleState(IntFlag):
    SGDSInactive = 0
    SGDSActive = 1
    SGDSActiveWithAutoPause = 3
    SGDSActiveUserDelimited = 4


class DISPID_SpeechPhraseRule(IntFlag):
    DISPID_SPRuleName = 1
    DISPID_SPRuleId = 2
    DISPID_SPRuleFirstElement = 3
    DISPID_SPRuleNumberOfElements = 4
    DISPID_SPRuleParent = 5
    DISPID_SPRuleChildren = 6
    DISPID_SPRuleConfidence = 7
    DISPID_SPRuleEngineConfidence = 8


class SpeechRecognizerState(IntFlag):
    SRSInactive = 0
    SRSActive = 1
    SRSActiveAlways = 2
    SRSInactiveWithPurge = 3


class DISPID_SpeechPhraseRules(IntFlag):
    DISPID_SPRulesCount = 1
    DISPID_SPRulesItem = 0
    DISPID_SPRules_NewEnum = -4


class SpeechVisemeFeature(IntFlag):
    SVF_None = 0
    SVF_Stressed = 1
    SVF_Emphasis = 2


class DISPID_SpeechLexicon(IntFlag):
    DISPID_SLGenerationId = 1
    DISPID_SLGetWords = 2
    DISPID_SLAddPronunciation = 3
    DISPID_SLAddPronunciationByPhoneIds = 4
    DISPID_SLRemovePronunciation = 5
    DISPID_SLRemovePronunciationByPhoneIds = 6
    DISPID_SLGetPronunciations = 7
    DISPID_SLGetGenerationChange = 8


class SpeechVisemeType(IntFlag):
    SVP_0 = 0
    SVP_1 = 1
    SVP_2 = 2
    SVP_3 = 3
    SVP_4 = 4
    SVP_5 = 5
    SVP_6 = 6
    SVP_7 = 7
    SVP_8 = 8
    SVP_9 = 9
    SVP_10 = 10
    SVP_11 = 11
    SVP_12 = 12
    SVP_13 = 13
    SVP_14 = 14
    SVP_15 = 15
    SVP_16 = 16
    SVP_17 = 17
    SVP_18 = 18
    SVP_19 = 19
    SVP_20 = 20
    SVP_21 = 21


class DISPID_SpeechLexiconWords(IntFlag):
    DISPID_SLWsCount = 1
    DISPID_SLWsItem = 0
    DISPID_SLWs_NewEnum = -4


class SpeechWordPronounceable(IntFlag):
    SWPUnknownWordUnpronounceable = 0
    SWPUnknownWordPronounceable = 1
    SWPKnownWordPronounceable = 2


class DISPID_SpeechLexiconWord(IntFlag):
    DISPID_SLWLangId = 1
    DISPID_SLWType = 2
    DISPID_SLWWord = 3
    DISPID_SLWPronunciations = 4


class DISPID_SpeechRecoResultTimes(IntFlag):
    DISPID_SRRTStreamTime = 1
    DISPID_SRRTLength = 2
    DISPID_SRRTTickCount = 3
    DISPID_SRRTOffsetFromStart = 4


class DISPID_SpeechLexiconProns(IntFlag):
    DISPID_SLPsCount = 1
    DISPID_SLPsItem = 0
    DISPID_SLPs_NewEnum = -4


class SPSEMANTICFORMAT(IntFlag):
    SPSMF_SAPI_PROPERTIES = 0
    SPSMF_SRGS_SEMANTICINTERPRETATION_MS = 1
    SPSMF_SRGS_SAPIPROPERTIES = 2
    SPSMF_UPS = 4
    SPSMF_SRGS_SEMANTICINTERPRETATION_W3C = 8


class DISPID_SpeechLexiconPronunciation(IntFlag):
    DISPID_SLPType = 1
    DISPID_SLPLangId = 2
    DISPID_SLPPartOfSpeech = 3
    DISPID_SLPPhoneIds = 4
    DISPID_SLPSymbolic = 5


class SPGRAMMARWORDTYPE(IntFlag):
    SPWT_DISPLAY = 0
    SPWT_LEXICAL = 1
    SPWT_PRONUNCIATION = 2
    SPWT_LEXICAL_NO_SPECIAL_CHARS = 3


class SPRULESTATE(IntFlag):
    SPRS_INACTIVE = 0
    SPRS_ACTIVE = 1
    SPRS_ACTIVE_WITH_AUTO_PAUSE = 3
    SPRS_ACTIVE_USER_DELIMITED = 4


class SPWORDPRONOUNCEABLE(IntFlag):
    SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE = 0
    SPWP_UNKNOWN_WORD_PRONOUNCEABLE = 1
    SPWP_KNOWN_WORD_PRONOUNCEABLE = 2


class SPGRAMMARSTATE(IntFlag):
    SPGS_DISABLED = 0
    SPGS_ENABLED = 1
    SPGS_EXCLUSIVE = 3


class SpeechAudioFormatType(IntFlag):
    SAFTDefault = -1
    SAFTNoAssignedFormat = 0
    SAFTText = 1
    SAFTNonStandardFormat = 2
    SAFTExtendedAudioFormat = 3
    SAFT8kHz8BitMono = 4
    SAFT8kHz8BitStereo = 5
    SAFT8kHz16BitMono = 6
    SAFT8kHz16BitStereo = 7
    SAFT11kHz8BitMono = 8
    SAFT11kHz8BitStereo = 9
    SAFT11kHz16BitMono = 10
    SAFT11kHz16BitStereo = 11
    SAFT12kHz8BitMono = 12
    SAFT12kHz8BitStereo = 13
    SAFT12kHz16BitMono = 14
    SAFT12kHz16BitStereo = 15
    SAFT16kHz8BitMono = 16
    SAFT16kHz8BitStereo = 17
    SAFT16kHz16BitMono = 18
    SAFT16kHz16BitStereo = 19
    SAFT22kHz8BitMono = 20
    SAFT22kHz8BitStereo = 21
    SAFT22kHz16BitMono = 22
    SAFT22kHz16BitStereo = 23
    SAFT24kHz8BitMono = 24
    SAFT24kHz8BitStereo = 25
    SAFT24kHz16BitMono = 26
    SAFT24kHz16BitStereo = 27
    SAFT32kHz8BitMono = 28
    SAFT32kHz8BitStereo = 29
    SAFT32kHz16BitMono = 30
    SAFT32kHz16BitStereo = 31
    SAFT44kHz8BitMono = 32
    SAFT44kHz8BitStereo = 33
    SAFT44kHz16BitMono = 34
    SAFT44kHz16BitStereo = 35
    SAFT48kHz8BitMono = 36
    SAFT48kHz8BitStereo = 37
    SAFT48kHz16BitMono = 38
    SAFT48kHz16BitStereo = 39
    SAFTTrueSpeech_8kHz1BitMono = 40
    SAFTCCITT_ALaw_8kHzMono = 41
    SAFTCCITT_ALaw_8kHzStereo = 42
    SAFTCCITT_ALaw_11kHzMono = 43
    SAFTCCITT_ALaw_11kHzStereo = 44
    SAFTCCITT_ALaw_22kHzMono = 45
    SAFTCCITT_ALaw_22kHzStereo = 46
    SAFTCCITT_ALaw_44kHzMono = 47
    SAFTCCITT_ALaw_44kHzStereo = 48
    SAFTCCITT_uLaw_8kHzMono = 49
    SAFTCCITT_uLaw_8kHzStereo = 50
    SAFTCCITT_uLaw_11kHzMono = 51
    SAFTCCITT_uLaw_11kHzStereo = 52
    SAFTCCITT_uLaw_22kHzMono = 53
    SAFTCCITT_uLaw_22kHzStereo = 54
    SAFTCCITT_uLaw_44kHzMono = 55
    SAFTCCITT_uLaw_44kHzStereo = 56
    SAFTADPCM_8kHzMono = 57
    SAFTADPCM_8kHzStereo = 58
    SAFTADPCM_11kHzMono = 59
    SAFTADPCM_11kHzStereo = 60
    SAFTADPCM_22kHzMono = 61
    SAFTADPCM_22kHzStereo = 62
    SAFTADPCM_44kHzMono = 63
    SAFTADPCM_44kHzStereo = 64
    SAFTGSM610_8kHzMono = 65
    SAFTGSM610_11kHzMono = 66
    SAFTGSM610_22kHzMono = 67
    SAFTGSM610_44kHzMono = 68


class DISPID_SpeechVoiceEvent(IntFlag):
    DISPID_SVEStreamStart = 1
    DISPID_SVEStreamEnd = 2
    DISPID_SVEVoiceChange = 3
    DISPID_SVEBookmark = 4
    DISPID_SVEWord = 5
    DISPID_SVEPhoneme = 6
    DISPID_SVESentenceBoundary = 7
    DISPID_SVEViseme = 8
    DISPID_SVEAudioLevel = 9
    DISPID_SVEEnginePrivate = 10


class SPFILEMODE(IntFlag):
    SPFM_OPEN_READONLY = 0
    SPFM_OPEN_READWRITE = 1
    SPFM_CREATE = 2
    SPFM_CREATE_ALWAYS = 3
    SPFM_NUM_MODES = 4


class DISPID_SpeechRecognizer(IntFlag):
    DISPID_SRRecognizer = 1
    DISPID_SRAllowAudioInputFormatChangesOnNextSet = 2
    DISPID_SRAudioInput = 3
    DISPID_SRAudioInputStream = 4
    DISPID_SRIsShared = 5
    DISPID_SRState = 6
    DISPID_SRStatus = 7
    DISPID_SRProfile = 8
    DISPID_SREmulateRecognition = 9
    DISPID_SRCreateRecoContext = 10
    DISPID_SRGetFormat = 11
    DISPID_SRSetPropertyNumber = 12
    DISPID_SRGetPropertyNumber = 13
    DISPID_SRSetPropertyString = 14
    DISPID_SRGetPropertyString = 15
    DISPID_SRIsUISupported = 16
    DISPID_SRDisplayUI = 17
    DISPID_SRGetRecognizers = 18
    DISPID_SVGetAudioInputs = 19
    DISPID_SVGetProfiles = 20


class SpeechTokenContext(IntFlag):
    STCInprocServer = 1
    STCInprocHandler = 2
    STCLocalServer = 4
    STCRemoteServer = 16
    STCAll = 23


class SpeechTokenShellFolder(IntFlag):
    STSF_AppData = 26
    STSF_LocalAppData = 28
    STSF_CommonAppData = 35
    STSF_FlagCreate = 32768


class SPVPRIORITY(IntFlag):
    SPVPRI_NORMAL = 0
    SPVPRI_ALERT = 1
    SPVPRI_OVER = 2


class SPEVENTENUM(IntFlag):
    SPEI_UNDEFINED = 0
    SPEI_START_INPUT_STREAM = 1
    SPEI_END_INPUT_STREAM = 2
    SPEI_VOICE_CHANGE = 3
    SPEI_TTS_BOOKMARK = 4
    SPEI_WORD_BOUNDARY = 5
    SPEI_PHONEME = 6
    SPEI_SENTENCE_BOUNDARY = 7
    SPEI_VISEME = 8
    SPEI_TTS_AUDIO_LEVEL = 9
    SPEI_TTS_PRIVATE = 15
    SPEI_MIN_TTS = 1
    SPEI_MAX_TTS = 15
    SPEI_END_SR_STREAM = 34
    SPEI_SOUND_START = 35
    SPEI_SOUND_END = 36
    SPEI_PHRASE_START = 37
    SPEI_RECOGNITION = 38
    SPEI_HYPOTHESIS = 39
    SPEI_SR_BOOKMARK = 40
    SPEI_PROPERTY_NUM_CHANGE = 41
    SPEI_PROPERTY_STRING_CHANGE = 42
    SPEI_FALSE_RECOGNITION = 43
    SPEI_INTERFERENCE = 44
    SPEI_REQUEST_UI = 45
    SPEI_RECO_STATE_CHANGE = 46
    SPEI_ADAPTATION = 47
    SPEI_START_SR_STREAM = 48
    SPEI_RECO_OTHER_CONTEXT = 49
    SPEI_SR_AUDIO_LEVEL = 50
    SPEI_SR_RETAINEDAUDIO = 51
    SPEI_SR_PRIVATE = 52
    SPEI_ACTIVE_CATEGORY_CHANGED = 53
    SPEI_RESERVED5 = 54
    SPEI_RESERVED6 = 55
    SPEI_MIN_SR = 34
    SPEI_MAX_SR = 55
    SPEI_RESERVED1 = 30
    SPEI_RESERVED2 = 33
    SPEI_RESERVED3 = 63


class SpeechEmulationCompareFlags(IntFlag):
    SECFIgnoreCase = 1
    SECFIgnoreKanaType = 65536
    SECFIgnoreWidth = 131072
    SECFNoSpecialChars = 536870912
    SECFEmulateResult = 1073741824
    SECFDefault = 196609


class DISPID_SpeechRecognizerStatus(IntFlag):
    DISPID_SRSAudioStatus = 1
    DISPID_SRSCurrentStreamPosition = 2
    DISPID_SRSCurrentStreamNumber = 3
    DISPID_SRSNumberOfActiveRules = 4
    DISPID_SRSClsidEngine = 5
    DISPID_SRSSupportedLanguages = 6


class DISPID_SpeechRecoContext(IntFlag):
    DISPID_SRCRecognizer = 1
    DISPID_SRCAudioInInterferenceStatus = 2
    DISPID_SRCRequestedUIType = 3
    DISPID_SRCVoice = 4
    DISPID_SRAllowVoiceFormatMatchingOnNextSet = 5
    DISPID_SRCVoicePurgeEvent = 6
    DISPID_SRCEventInterests = 7
    DISPID_SRCCmdMaxAlternates = 8
    DISPID_SRCState = 9
    DISPID_SRCRetainedAudio = 10
    DISPID_SRCRetainedAudioFormat = 11
    DISPID_SRCPause = 12
    DISPID_SRCResume = 13
    DISPID_SRCCreateGrammar = 14
    DISPID_SRCCreateResultFromMemory = 15
    DISPID_SRCBookmark = 16
    DISPID_SRCSetAdaptationData = 17


class SPVISEMES(IntFlag):
    SP_VISEME_0 = 0
    SP_VISEME_1 = 1
    SP_VISEME_2 = 2
    SP_VISEME_3 = 3
    SP_VISEME_4 = 4
    SP_VISEME_5 = 5
    SP_VISEME_6 = 6
    SP_VISEME_7 = 7
    SP_VISEME_8 = 8
    SP_VISEME_9 = 9
    SP_VISEME_10 = 10
    SP_VISEME_11 = 11
    SP_VISEME_12 = 12
    SP_VISEME_13 = 13
    SP_VISEME_14 = 14
    SP_VISEME_15 = 15
    SP_VISEME_16 = 16
    SP_VISEME_17 = 17
    SP_VISEME_18 = 18
    SP_VISEME_19 = 19
    SP_VISEME_20 = 20
    SP_VISEME_21 = 21


class DISPID_SpeechObjectToken(IntFlag):
    DISPID_SOTId = 1
    DISPID_SOTDataKey = 2
    DISPID_SOTCategory = 3
    DISPID_SOTGetDescription = 4
    DISPID_SOTSetId = 5
    DISPID_SOTGetAttribute = 6
    DISPID_SOTCreateInstance = 7
    DISPID_SOTRemove = 8
    DISPID_SOTGetStorageFileName = 9
    DISPID_SOTRemoveStorageFileName = 10
    DISPID_SOTIsUISupported = 11
    DISPID_SOTDisplayUI = 12
    DISPID_SOTMatchesAttributes = 13


class DISPID_SpeechDataKey(IntFlag):
    DISPID_SDKSetBinaryValue = 1
    DISPID_SDKGetBinaryValue = 2
    DISPID_SDKSetStringValue = 3
    DISPID_SDKGetStringValue = 4
    DISPID_SDKSetLongValue = 5
    DISPID_SDKGetlongValue = 6
    DISPID_SDKOpenKey = 7
    DISPID_SDKCreateKey = 8
    DISPID_SDKDeleteKey = 9
    DISPID_SDKDeleteValue = 10
    DISPID_SDKEnumKeys = 11
    DISPID_SDKEnumValues = 12


class SpeechInterference(IntFlag):
    SINone = 0
    SINoise = 1
    SINoSignal = 2
    SITooLoud = 3
    SITooQuiet = 4
    SITooFast = 5
    SITooSlow = 6


class SpeechRecoEvents(IntFlag):
    SREStreamEnd = 1
    SRESoundStart = 2
    SRESoundEnd = 4
    SREPhraseStart = 8
    SRERecognition = 16
    SREHypothesis = 32
    SREBookmark = 64
    SREPropertyNumChange = 128
    SREPropertyStringChange = 256
    SREFalseRecognition = 512
    SREInterference = 1024
    SRERequestUI = 2048
    SREStateChange = 4096
    SREAdaptation = 8192
    SREStreamStart = 16384
    SRERecoOtherContext = 32768
    SREAudioLevel = 65536
    SREPrivate = 262144
    SREAllEvents = 393215


class SpeechRecoContextState(IntFlag):
    SRCS_Disabled = 0
    SRCS_Enabled = 1


class SpeechRetainedAudioOptions(IntFlag):
    SRAONone = 0
    SRAORetainAudio = 1


class DISPID_SpeechObjectTokens(IntFlag):
    DISPID_SOTsCount = 1
    DISPID_SOTsItem = 0
    DISPID_SOTs_NewEnum = -4


class DISPIDSPRG(IntFlag):
    DISPID_SRGId = 1
    DISPID_SRGRecoContext = 2
    DISPID_SRGState = 3
    DISPID_SRGRules = 4
    DISPID_SRGReset = 5
    DISPID_SRGCommit = 6
    DISPID_SRGCmdLoadFromFile = 7
    DISPID_SRGCmdLoadFromObject = 8
    DISPID_SRGCmdLoadFromResource = 9
    DISPID_SRGCmdLoadFromMemory = 10
    DISPID_SRGCmdLoadFromProprietaryGrammar = 11
    DISPID_SRGCmdSetRuleState = 12
    DISPID_SRGCmdSetRuleIdState = 13
    DISPID_SRGDictationLoad = 14
    DISPID_SRGDictationUnload = 15
    DISPID_SRGDictationSetState = 16
    DISPID_SRGSetWordSequenceData = 17
    DISPID_SRGSetTextSelection = 18
    DISPID_SRGIsPronounceable = 19


class SpeechStreamSeekPositionType(IntFlag):
    SSSPTRelativeToStart = 0
    SSSPTRelativeToCurrentPosition = 1
    SSSPTRelativeToEnd = 2


class DISPID_SpeechObjectTokenCategory(IntFlag):
    DISPID_SOTCId = 1
    DISPID_SOTCDefault = 2
    DISPID_SOTCSetId = 3
    DISPID_SOTCGetDataKey = 4
    DISPID_SOTCEnumerateTokens = 5


class SpeechDataKeyLocation(IntFlag):
    SDKLDefaultLocation = 0
    SDKLCurrentUser = 1
    SDKLLocalMachine = 2
    SDKLCurrentConfig = 5


class DISPID_SpeechAudioFormat(IntFlag):
    DISPID_SAFType = 1
    DISPID_SAFGuid = 2
    DISPID_SAFGetWaveFormatEx = 3
    DISPID_SAFSetWaveFormatEx = 4


class DISPID_SpeechRecoContextEvents(IntFlag):
    DISPID_SRCEStartStream = 1
    DISPID_SRCEEndStream = 2
    DISPID_SRCEBookmark = 3
    DISPID_SRCESoundStart = 4
    DISPID_SRCESoundEnd = 5
    DISPID_SRCEPhraseStart = 6
    DISPID_SRCERecognition = 7
    DISPID_SRCEHypothesis = 8
    DISPID_SRCEPropertyNumberChange = 9
    DISPID_SRCEPropertyStringChange = 10
    DISPID_SRCEFalseRecognition = 11
    DISPID_SRCEInterference = 12
    DISPID_SRCERequestUI = 13
    DISPID_SRCERecognizerStateChange = 14
    DISPID_SRCEAdaptation = 15
    DISPID_SRCERecognitionForOtherContext = 16
    DISPID_SRCEAudioLevel = 17
    DISPID_SRCEEnginePrivate = 18


class DISPID_SpeechBaseStream(IntFlag):
    DISPID_SBSFormat = 1
    DISPID_SBSRead = 2
    DISPID_SBSWrite = 3
    DISPID_SBSSeek = 4


class DISPID_SpeechAudio(IntFlag):
    DISPID_SAStatus = 200
    DISPID_SABufferInfo = 201
    DISPID_SADefaultFormat = 202
    DISPID_SAVolume = 203
    DISPID_SABufferNotifySize = 204
    DISPID_SAEventHandle = 205
    DISPID_SASetState = 206


class DISPID_SpeechMMSysAudio(IntFlag):
    DISPID_SMSADeviceId = 300
    DISPID_SMSALineId = 301
    DISPID_SMSAMMHandle = 302


class DISPID_SpeechGrammarRule(IntFlag):
    DISPID_SGRAttributes = 1
    DISPID_SGRInitialState = 2
    DISPID_SGRName = 3
    DISPID_SGRId = 4
    DISPID_SGRClear = 5
    DISPID_SGRAddResource = 6
    DISPID_SGRAddState = 7


class DISPID_SpeechFileStream(IntFlag):
    DISPID_SFSOpen = 100
    DISPID_SFSClose = 101


class SPINTERFERENCE(IntFlag):
    SPINTERFERENCE_NONE = 0
    SPINTERFERENCE_NOISE = 1
    SPINTERFERENCE_NOSIGNAL = 2
    SPINTERFERENCE_TOOLOUD = 3
    SPINTERFERENCE_TOOQUIET = 4
    SPINTERFERENCE_TOOFAST = 5
    SPINTERFERENCE_TOOSLOW = 6
    SPINTERFERENCE_LATENCY_WARNING = 7
    SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN = 8
    SPINTERFERENCE_LATENCY_TRUNCATE_END = 9


class DISPID_SpeechCustomStream(IntFlag):
    DISPID_SCSBaseStream = 100


class SpeechStreamFileMode(IntFlag):
    SSFMOpenForRead = 0
    SSFMOpenReadWrite = 1
    SSFMCreate = 2
    SSFMCreateForWrite = 3


class DISPID_SpeechMemoryStream(IntFlag):
    DISPID_SMSSetData = 100
    DISPID_SMSGetData = 101


class DISPID_SpeechGrammarRules(IntFlag):
    DISPID_SGRsCount = 1
    DISPID_SGRsDynamic = 2
    DISPID_SGRsAdd = 3
    DISPID_SGRsCommit = 4
    DISPID_SGRsCommitAndSave = 5
    DISPID_SGRsFindRule = 6
    DISPID_SGRsItem = 0
    DISPID_SGRs_NewEnum = -4


class DISPID_SpeechAudioStatus(IntFlag):
    DISPID_SASFreeBufferSpace = 1
    DISPID_SASNonBlockingIO = 2
    DISPID_SASState = 3
    DISPID_SASCurrentSeekPosition = 4
    DISPID_SASCurrentDevicePosition = 5


class DISPID_SpeechAudioBufferInfo(IntFlag):
    DISPID_SABIMinNotification = 1
    DISPID_SABIBufferSize = 2
    DISPID_SABIEventBias = 3


class DISPID_SpeechGrammarRuleState(IntFlag):
    DISPID_SGRSRule = 1
    DISPID_SGRSTransitions = 2
    DISPID_SGRSAddWordTransition = 3
    DISPID_SGRSAddRuleTransition = 4
    DISPID_SGRSAddSpecialTransition = 5


class DISPID_SpeechWaveFormatEx(IntFlag):
    DISPID_SWFEFormatTag = 1
    DISPID_SWFEChannels = 2
    DISPID_SWFESamplesPerSec = 3
    DISPID_SWFEAvgBytesPerSec = 4
    DISPID_SWFEBlockAlign = 5
    DISPID_SWFEBitsPerSample = 6
    DISPID_SWFEExtraData = 7


class DISPID_SpeechVoice(IntFlag):
    DISPID_SVStatus = 1
    DISPID_SVVoice = 2
    DISPID_SVAudioOutput = 3
    DISPID_SVAudioOutputStream = 4
    DISPID_SVRate = 5
    DISPID_SVVolume = 6
    DISPID_SVAllowAudioOuputFormatChangesOnNextSet = 7
    DISPID_SVEventInterests = 8
    DISPID_SVPriority = 9
    DISPID_SVAlertBoundary = 10
    DISPID_SVSyncronousSpeakTimeout = 11
    DISPID_SVSpeak = 12
    DISPID_SVSpeakStream = 13
    DISPID_SVPause = 14
    DISPID_SVResume = 15
    DISPID_SVSkip = 16
    DISPID_SVGetVoices = 17
    DISPID_SVGetAudioOutputs = 18
    DISPID_SVWaitUntilDone = 19
    DISPID_SVSpeakCompleteEvent = 20
    DISPID_SVIsUISupported = 21
    DISPID_SVDisplayUI = 22


class DISPID_SpeechGrammarRuleStateTransitions(IntFlag):
    DISPID_SGRSTsCount = 1
    DISPID_SGRSTsItem = 0
    DISPID_SGRSTs_NewEnum = -4


class SPAUDIOOPTIONS(IntFlag):
    SPAO_NONE = 0
    SPAO_RETAIN_AUDIO = 1


class SpeechGrammarState(IntFlag):
    SGSEnabled = 1
    SGSDisabled = 0
    SGSExclusive = 3


class DISPID_SpeechGrammarRuleStateTransition(IntFlag):
    DISPID_SGRSTType = 1
    DISPID_SGRSTText = 2
    DISPID_SGRSTRule = 3
    DISPID_SGRSTWeight = 4
    DISPID_SGRSTPropertyName = 5
    DISPID_SGRSTPropertyId = 6
    DISPID_SGRSTPropertyValue = 7
    DISPID_SGRSTNextState = 8


class DISPID_SpeechVoiceStatus(IntFlag):
    DISPID_SVSCurrentStreamNumber = 1
    DISPID_SVSLastStreamNumberQueued = 2
    DISPID_SVSLastResult = 3
    DISPID_SVSRunningState = 4
    DISPID_SVSInputWordPosition = 5
    DISPID_SVSInputWordLength = 6
    DISPID_SVSInputSentencePosition = 7
    DISPID_SVSInputSentenceLength = 8
    DISPID_SVSLastBookmark = 9
    DISPID_SVSLastBookmarkId = 10
    DISPID_SVSPhonemeId = 11
    DISPID_SVSVisemeId = 12


class SpeechAudioState(IntFlag):
    SASClosed = 0
    SASStop = 1
    SASPause = 2
    SASRun = 3


class DISPIDSPTSI(IntFlag):
    DISPIDSPTSI_ActiveOffset = 1
    DISPIDSPTSI_ActiveLength = 2
    DISPIDSPTSI_SelectionOffset = 3
    DISPIDSPTSI_SelectionLength = 4


class DISPID_SpeechRecoResult(IntFlag):
    DISPID_SRRRecoContext = 1
    DISPID_SRRTimes = 2
    DISPID_SRRAudioFormat = 3
    DISPID_SRRPhraseInfo = 4
    DISPID_SRRAlternates = 5
    DISPID_SRRAudio = 6
    DISPID_SRRSpeakAudio = 7
    DISPID_SRRSaveToMemory = 8
    DISPID_SRRDiscardResultInfo = 9


class DISPID_SpeechXMLRecoResult(IntFlag):
    DISPID_SRRGetXMLResult = 10
    DISPID_SRRGetXMLErrorInfo = 11


class DISPID_SpeechPhoneConverter(IntFlag):
    DISPID_SPCLangId = 1
    DISPID_SPCPhoneToId = 2
    DISPID_SPCIdToPhone = 3


SPAUDIOSTATE = _SPAUDIOSTATE
SPSTREAMFORMATTYPE = SPWAVEFORMATTYPE


__all__ = [
    'DISPID_SVSInputSentencePosition', 'SECFIgnoreCase',
    'SPRECOGNIZERSTATUS', 'DISPID_SpeechBaseStream', 'LONG_PTR',
    'SAFT16kHz16BitMono', '_SPAUDIOSTATE', 'DISPID_SPEDisplayText',
    'SPEI_SR_BOOKMARK', 'DISPID_SRGSetTextSelection',
    'SPFM_OPEN_READWRITE', 'SPDKL_CurrentConfig',
    'DISPID_SRGSetWordSequenceData', 'SpeechDataKeyLocation',
    'DISPID_SPPValue', 'SVP_13', 'SpMMAudioEnum', 'SGDSInactive',
    'DISPID_SpeechCustomStream', 'SAFTCCITT_uLaw_8kHzStereo',
    'SPINTERFERENCE_NONE', 'DISPID_SDKDeleteValue',
    'ISpeechWaveFormatEx', 'STSF_FlagCreate', 'SRTReSent',
    'eLEXTYPE_PRIVATE8', 'SpeechTokenKeyAttributes', 'IEnumString',
    'SPRS_INACTIVE', 'SAFTADPCM_11kHzStereo', 'SpeechAddRemoveWord',
    'DISPID_SPRuleParent', 'SPWT_DISPLAY', 'SVSFNLPMask',
    'Speech_Max_Word_Length', 'SAFT44kHz16BitMono', 'SREPhraseStart',
    'DISPID_SRCERecognizerStateChange', 'ISpeechMMSysAudio',
    'SPPS_RESERVED4', 'SpeechStreamFileMode',
    'DISPID_SASFreeBufferSpace', 'DISPID_SOTGetDescription',
    'DISPID_SPEEngineConfidence', 'SpeechGrammarState', 'SPWORD',
    'SpFileStream', 'SPSUnknown', 'DISPIDSPRG',
    'SAFTCCITT_ALaw_8kHzStereo', 'DISPID_SRCERecognition',
    'SP_VISEME_16', 'DISPID_SRAudioInput', 'DISPID_SGRs_NewEnum',
    'SVEAudioLevel', 'DISPID_SVSLastBookmarkId', 'SPPS_Noncontent',
    'SPPHRASE', 'DISPID_SRCERecognitionForOtherContext',
    'DISPID_SRSCurrentStreamPosition', 'SGRSTTWord', 'DISPID_SRState',
    'SPPHRASERULE', 'SpNotifyTranslator',
    'DISPID_SPPBRestorePhraseFromMemory', 'SRERecoOtherContext',
    'DISPID_SMSADeviceId', 'DISPID_SRRAlternates',
    'DISPID_SPPConfidence', 'SpeechRecognitionType', 'SPSLMA',
    'SPRS_ACTIVE_WITH_AUTO_PAUSE', 'SAFTCCITT_ALaw_11kHzMono',
    'ISpRecoContext', 'eLEXTYPE_PRIVATE9',
    'DISPID_SASCurrentSeekPosition', 'DISPID_SBSSeek',
    'ISpeechMemoryStream', 'DISPID_SpeechLexiconWords',
    'DISPID_SOTsItem', 'SLTApp', 'DISPID_SLWsCount',
    'SREPropertyStringChange', 'DISPID_SRRSetTextFeedback',
    'DISPID_SRCESoundEnd', 'DISPID_SRSetPropertyString',
    'DISPID_SGRsFindRule', 'DISPID_SPPId',
    'SpeechGrammarTagUnlimitedDictation',
    'DISPID_SPEActualConfidence', 'eLEXTYPE_PRIVATE14',
    'SpeechRuleState', 'DISPID_SPIReplacements', 'DISPID_SRIsShared',
    'DISPID_SpeechPhraseElement', 'SPAS_STOP', 'SPGRAMMARSTATE',
    'SPAR_Low', 'SpStreamFormatConverter', 'SRTStandard',
    'SPCT_DICTATION', 'SPTEXTSELECTIONINFO', 'DISPID_SRCEEndStream',
    'ISpeechPhraseAlternates', 'SSTTDictation', 'SGSDisabled',
    'ISpeechObjectToken', 'SPWAVEFORMATTYPE', 'SpeechAllElements',
    'SVPAlert', 'DISPID_SPIProperties', 'SPWORDTYPE',
    'DISPID_SPPParent', 'SpeechTokenValueCLSID',
    'DISPID_SRAudioInputStream', 'DISPID_SRGetPropertyString',
    'SPAS_CLOSED', 'DISPID_SPIAudioStreamPosition', 'DISPID_SPRsItem',
    'SAFT44kHz8BitMono', 'STCAll', 'SREPrivate',
    'DISPID_SRRPhraseInfo', 'DISPID_SpeechPhraseReplacements',
    'SpeechPropertyResourceUsage', 'SVEAllEvents',
    'SAFT32kHz8BitMono', 'SPEI_RESERVED6', 'SVP_14', 'SREStreamStart',
    'DISPID_SpeechRecoResult', 'ISpeechGrammarRule',
    'SPRECORESULTTIMES', 'SpNullPhoneConverter',
    'SAFT48kHz16BitStereo', 'SASRun', 'SAFTExtendedAudioFormat',
    'SPEI_RESERVED3', 'eLEXTYPE_USER', 'SREAllEvents', 'SPEVENTENUM',
    'DISPID_SPIGetText', 'ISpPhrase', 'SFTInput', 'DISPID_SPRsCount',
    'DISPID_SPERetainedSizeBytes', 'ISpRecoResult',
    'SpeechVoiceSkipTypeSentence', 'DISPID_SRGDictationSetState',
    'SPEI_FALSE_RECOGNITION', 'SSFMOpenForRead', 'DISPID_SPCLangId',
    'DISPID_SPEDisplayAttributes', 'eLEXTYPE_RESERVED6', 'SWTDeleted',
    'DISPID_SPIGetDisplayAttributes',
    'ISpeechTextSelectionInformation', 'SPAO_NONE',
    'DISPID_SpeechGrammarRuleState',
    'DISPID_SRCAudioInInterferenceStatus',
    'DISPID_SRGCmdLoadFromObject', 'SP_VISEME_20',
    'DISPID_SpeechGrammarRules', 'SRESoundEnd', 'SPRST_NUM_STATES',
    'SPAR_Medium', 'SVP_10', 'SAFT11kHz16BitMono',
    'DISPID_SGRAttributes', 'SPSERIALIZEDRESULT', 'ISpeechPhraseInfo',
    'SITooSlow', 'DISPID_SVAllowAudioOuputFormatChangesOnNextSet',
    'DISPID_SGRSTWeight', 'SPVISEMES', 'DISPID_SRCreateRecoContext',
    'SPWF_SRENGINE', 'SPEI_SOUND_END', 'SpeechSpecialTransitionType',
    'SPPS_NotOverriden', 'DISPID_SPPFirstElement', 'SPSMF_UPS',
    'SP_VISEME_3', 'SpeechCategoryVoices', 'SP_VISEME_6',
    'SRERequestUI', 'ISpObjectTokenCategory', 'DISPID_SpeechAudio',
    'WAVEFORMATEX', 'DISPID_SpeechAudioStatus',
    'DISPID_SGRSTPropertyId', 'SINoSignal', 'DISPID_SAFGuid',
    'SAFT48kHz8BitMono', 'ISpeechRecoResultDispatch',
    'DISPID_SRCEPropertyStringChange', 'IEnumSpObjectTokens',
    'DISPID_SpeechPhraseRule',
    'SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN', 'SSTTWildcard',
    'SPPS_LMA', 'DISPID_SPEs_NewEnum', 'DISPID_SRCEventInterests',
    'DISPID_SGRsItem', 'ISpEventSink', 'DISPID_SOTsCount',
    'SPGS_ENABLED', 'SWPUnknownWordUnpronounceable',
    'SSFMOpenReadWrite', 'DISPID_SPPs_NewEnum', 'SAFT44kHz8BitStereo',
    'eWORDTYPE_ADDED', 'ISpeechPhraseElement',
    'DISPID_SDKGetStringValue', 'SpeechTokenKeyFiles',
    'SPPS_Function', 'SPSERIALIZEDPHRASE',
    'DISPID_SpeechPhraseProperty', 'ISpeechRecoResult',
    'ISpeechRecoResultTimes', 'SPPS_Modifier', 'DISPID_SFSClose',
    'SAFT22kHz16BitStereo', 'SP_VISEME_2', 'SSSPTRelativeToEnd',
    'SASPause', 'ISpNotifySink', 'SRSEDone',
    'SpeechGrammarTagDictation', 'SPEI_VOICE_CHANGE',
    'DISPID_SPAPhraseInfo', 'SVF_None', 'SREStreamEnd',
    'DISPID_SVStatus', 'DISPID_SPCIdToPhone',
    'DISPID_SVSCurrentStreamNumber', 'SPEI_INTERFERENCE',
    'SPSHORTCUTTYPE', 'SPWT_PRONUNCIATION', 'DISPID_SRRSaveToMemory',
    'SDKLLocalMachine', 'DISPID_SOTDisplayUI', 'SRTAutopause',
    'DISPID_SpeechLexicon', 'DISPID_SGRSTs_NewEnum',
    'DISPID_SVEStreamStart', 'DISPID_SOTMatchesAttributes',
    'DISPID_SVEPhoneme', 'DISPID_SpeechGrammarRule',
    'SpeechPartOfSpeech', 'SAFTCCITT_ALaw_8kHzMono',
    'DISPID_SVSpeakStream', 'SpSharedRecognizer', 'SAFTDefault',
    'ISpXMLRecoResult', 'SREPropertyNumChange', 'SVSFIsXML',
    'SPLO_STATIC', 'DISPID_SMSGetData', 'SINoise',
    'SPINTERFERENCE_TOOQUIET', 'SPFM_CREATE', 'SPWORDLIST',
    'DISPID_SPEsCount', 'SWPKnownWordPronounceable', 'SDTAll',
    'SPINTERFERENCE_LATENCY_WARNING', 'SP_VISEME_17',
    'SECFIgnoreKanaType', 'DISPID_SRGRecoContext',
    'ISpeechPhraseElements', 'DISPID_SOTCDefault',
    'DISPID_SOTCreateInstance', 'SPEVENT', 'DISPID_SRCEAdaptation',
    'DISPID_SPAStartElementInResult', 'DISPID_SRCEAudioLevel',
    'DISPID_SRRTimes', 'SVP_9', 'SPEI_TTS_BOOKMARK',
    'DISPID_SVGetAudioInputs', 'SPEI_TTS_AUDIO_LEVEL',
    'SAFT12kHz16BitMono', 'SVP_19', 'SpeechWordPronounceable',
    'SP_VISEME_9', 'SAFT8kHz16BitMono', 'SPEI_PHRASE_START',
    'STSF_AppData', 'SpeechGrammarRuleStateTransitionType',
    'SpeechStreamSeekPositionType', 'SpeechCategoryAppLexicons',
    'DISPID_SGRSTNextState', 'SpeechFormatType',
    'DISPID_SLGenerationId', 'SAFTADPCM_8kHzStereo',
    'SpeechPropertyHighConfidenceThreshold', 'SECFNoSpecialChars',
    'SpeechEmulationCompareFlags', 'DISPID_SRProfile',
    'DISPID_SVGetVoices', 'SDA_Consume_Leading_Spaces',
    'eLEXTYPE_PRIVATE10', 'DISPID_SVDisplayUI', 'SECHighConfidence',
    'SpCompressedLexicon', 'SVEPhoneme', 'SGSEnabled', 'SP_VISEME_19',
    'DISPID_SRRTOffsetFromStart', 'SpeechRecoContextState',
    'DISPID_SpeechRecoResult2', 'SP_VISEME_11', 'STCRemoteServer',
    'eLEXTYPE_RESERVED4', 'STCInprocServer',
    'SAFTCCITT_ALaw_22kHzMono', 'DISPID_SPPEngineConfidence',
    'SAFTText', 'DISPID_SVSInputSentenceLength',
    'DISPID_SADefaultFormat', 'DISPID_SRCVoicePurgeEvent',
    'SPLOADOPTIONS', 'SGLexical', 'SECLowConfidence', 'SDTAlternates',
    'SVEEndInputStream', 'DISPID_SGRId', 'SpeechCategoryRecoProfiles',
    'SAFT24kHz16BitStereo', 'SPEI_RESERVED1',
    'ISpeechAudioBufferInfo', 'ISpPhoneticAlphabetSelection',
    'SRSEIsSpeaking', 'SP_VISEME_1', 'SINone',
    'ISpeechPhraseReplacement', 'SPEI_SR_AUDIO_LEVEL',
    'SpeechVoiceSpeakFlags', 'ISpeechGrammarRuleState',
    'SDTPronunciation', 'SECNormalConfidence', 'SPCT_SUB_COMMAND',
    'SPSHT_Unknown', 'SPSNoun', 'DISPID_SASetState',
    'ISpeechRecoResult2', 'DISPID_SMSSetData', 'DISPID_SVVolume',
    'SPSHT_EMAIL', 'SAFTCCITT_ALaw_22kHzStereo', 'SpWaveFormatEx',
    'DISPID_SPRuleNumberOfElements', 'SVSFParseSapi', 'SP_VISEME_8',
    'DISPID_SOTs_NewEnum', 'SDA_One_Trailing_Space', 'SPPS_Verb',
    'SPINTERFERENCE_NOISE', 'DISPID_SRGDictationUnload',
    'SPSHT_NotOverriden', 'DISPID_SRCRequestedUIType', 'SVP_2',
    'SPVPRIORITY', 'Speech_StreamPos_RealTime', 'eLEXTYPE_RESERVED9',
    'DISPID_SRCEStartStream', 'DISPID_SWFEChannels',
    'DISPID_SRRSpeakAudio', 'ISpVoice', 'SRTSMLTimeout',
    'SREAudioLevel', 'eLEXTYPE_PRIVATE11', 'tagSTATSTG',
    'DISPID_SRCRetainedAudio', 'ISpeechRecoContext', 'DISPID_SRGId',
    'DISPID_SpeechWaveFormatEx', 'DISPID_SRCERequestUI',
    'SPPS_RESERVED3', 'SPWORDPRONUNCIATION', 'SPSFunction',
    'DISPID_SMSAMMHandle', 'DISPID_SGRClear', 'ISpShortcut',
    'DISPID_SPRuleFirstElement', 'SSFMCreate',
    'SAFTCCITT_uLaw_22kHzMono', 'SpeechGrammarTagWildcard',
    'SPEI_REQUEST_UI', 'SPSEMANTICFORMAT', 'SPBO_PAUSE',
    'SpeechAudioState', 'SpPhoneticAlphabetConverter',
    'SVSFParseAutodetect', 'DISPID_SASCurrentDevicePosition',
    'DISPID_SOTCGetDataKey', 'DISPID_SAFGetWaveFormatEx',
    'SDKLDefaultLocation', 'SPPS_RESERVED2', 'DISPID_SABIBufferSize',
    'SpeechCategoryAudioOut', 'SDA_No_Trailing_Space', 'SASClosed',
    'SPEI_HYPOTHESIS', 'ISpStreamFormat', 'DISPID_SPIEngineId',
    'DISPID_SLPType', 'SP_VISEME_7', 'SAFTCCITT_ALaw_44kHzMono',
    'SPEVENTSOURCEINFO', 'SAFT22kHz8BitStereo', 'SPFM_NUM_MODES',
    'SAFT24kHz8BitMono', 'SPSNotOverriden', 'DISPID_SWFEBlockAlign',
    'DISPID_SRCEEnginePrivate', 'SpeechPropertyResponseSpeed',
    'DISPID_SVResume', 'SpeechInterference',
    'DISPID_SpeechGrammarRuleStateTransition', 'SVSFIsNotXML',
    'SVEPrivate', 'ISpRecoGrammar', 'SAFT24kHz8BitStereo',
    'SAFTCCITT_uLaw_11kHzMono', 'STCInprocHandler', 'SRCS_Disabled',
    'DISPID_SRCEPropertyNumberChange', 'SVSFDefault',
    'DISPID_SGRsAdd', 'SPWORDPRONUNCIATIONLIST',
    'DISPID_SPEPronunciation', 'ISpeechAudioStatus',
    'SAFTCCITT_ALaw_44kHzStereo', 'SDTLexicalForm', 'SSTTTextBuffer',
    'eLEXTYPE_PRIVATE13', 'DISPID_SpeechFileStream',
    'DISPID_SpeechRecoContextEvents', 'SPEI_SR_PRIVATE', 'SRARoot',
    'SECFEmulateResult', 'ISpNotifyTranslator', 'ISpProperties',
    'DISPID_SPACommit', 'DISPID_SPEsItem',
    'DISPID_SRSSupportedLanguages', 'SPINTERFERENCE_NOSIGNAL',
    'SP_VISEME_15', 'SVP_5', 'DISPID_SpeechMemoryStream',
    'DISPID_SPRFirstElement', 'DISPID_SGRSTsItem',
    'ISpResourceManager', 'SVP_15', 'eLEXTYPE_APP', 'SpObjectToken',
    'SpTextSelectionInformation', 'SPWORDPRONOUNCEABLE',
    'DISPID_SPPName', 'SVSFPersistXML', 'DISPID_SDKDeleteKey',
    'DISPID_SLRemovePronunciationByPhoneIds', 'SpPhoneConverter',
    'SFTSREngine', 'SpeechGrammarWordType', 'DISPID_SDKSetLongValue',
    'DISPID_SPCPhoneToId', 'ISpRecognizer', 'DISPID_SRCVoice',
    'SDTAudio', 'ISpObjectToken', 'SAFT12kHz8BitStereo',
    'SRESoundStart', 'DISPID_SPARecoResult', 'ISpeechObjectTokens',
    'DISPID_SABIMinNotification', 'DISPID_SVIsUISupported',
    'SPDATAKEYLOCATION', 'SPVPRI_OVER', 'DISPID_SRCEPhraseStart',
    'DISPID_SpeechObjectToken', 'eLEXTYPE_MORPHOLOGY',
    'DISPID_SGRSTType', 'SPRULE', 'SPSHT_OTHER',
    'DISPID_SGRAddResource', 'DISPID_SpeechPhraseProperties',
    'DISPID_SGRInitialState', 'DISPID_SRRDiscardResultInfo',
    'SRAImport', 'eLEXTYPE_PRIVATE4', 'SpLexicon', 'SVP_0',
    'SPEI_ACTIVE_CATEGORY_CHANGED', 'SAFT12kHz8BitMono',
    'DISPID_SDKGetBinaryValue', 'SpCustomStream',
    'DISPID_SpeechGrammarRuleStateTransitions', 'ISpPhoneConverter',
    'eWORDTYPE_DELETED', 'eLEXTYPE_RESERVED8', 'SAFT48kHz8BitStereo',
    'SPGS_EXCLUSIVE', 'DISPID_SPERetainedStreamOffset',
    'ISpeechLexiconPronunciations', 'DISPID_SAFSetWaveFormatEx',
    'DISPID_SGRSAddWordTransition', 'DISPID_SRRAudio',
    'ISpeechPhraseRules', 'DISPID_SLPPartOfSpeech',
    'SpeechDictationTopicSpelling', 'SASStop', 'SPCS_DISABLED',
    'DISPID_SPAsCount', 'SAFTGSM610_11kHzMono',
    'DISPID_SVSLastBookmark', 'DISPID_SRGCmdLoadFromMemory',
    'SRSInactive', 'DISPID_SpeechLexiconProns', 'SPSInterjection',
    'SRADynamic', 'SREBookmark', 'DISPID_SVSkip', '_RemotableHandle',
    '_ISpeechRecoContextEvents', 'SpeechDisplayAttributes',
    'SPAR_High', 'SREHypothesis', 'SVEViseme', 'DISPID_SLGetWords',
    'DISPID_SVEVoiceChange', 'SAFT8kHz8BitMono', 'SLODynamic',
    'ISpPhraseAlt', 'SpeechAudioFormatType', 'DISPID_SLPsCount',
    'SAFTNonStandardFormat', 'DISPID_SVSLastResult',
    'DISPID_SGRsCommit', 'DISPID_SVRate', 'ISpLexicon',
    'IInternetSecurityMgrSite', 'SpeechCategoryPhoneConverters',
    'SVSFParseMask', 'DISPID_SOTGetAttribute',
    'DISPID_SRRGetXMLErrorInfo', 'SPINTERFERENCE_TOOLOUD',
    'ISpEventSource', 'DISPID_SPIStartTime', 'SAFT48kHz16BitMono',
    'Library', 'SPAUDIOBUFFERINFO', 'SpInprocRecognizer',
    'DISPID_SPIRetainedSizeBytes', 'SVPOver', 'SpeechRunState',
    'Speech_Default_Weight', 'SPAUDIOOPTIONS', 'SPSModifier',
    'DISPIDSPTSI_SelectionOffset', 'SVF_Emphasis',
    'DISPID_SpeechPhraseAlternate', 'SPCS_ENABLED', 'ISpStream',
    'SVP_3', 'typelib_path', 'DISPID_SDKSetStringValue',
    'DISPID_SPIAudioSizeBytes', 'DISPID_SGRSAddRuleTransition',
    'SAFTNoAssignedFormat', 'ISpPhoneticAlphabetConverter',
    'DISPID_SGRName', 'SECFIgnoreWidth', 'SPFILEMODE',
    'ISpeechPhraseProperty', 'DISPID_SpeechVoiceStatus',
    'SpeechMicTraining', 'DISPID_SGRSRule', 'eLEXTYPE_PRIVATE19',
    'DISPID_SpeechPhraseRules', 'DISPID_SPPChildren',
    'DISPID_SLPsItem', 'SpeechUserTraining',
    'SPEI_RECO_OTHER_CONTEXT', 'SAFT32kHz8BitStereo',
    'DISPID_SRGCmdLoadFromProprietaryGrammar', 'DISPID_SAVolume',
    'DISPID_SVVoice', 'DISPID_SRCEInterference',
    'DISPID_SpeechPhoneConverter', 'DISPID_SOTCategory',
    'DISPID_SPEAudioTimeOffset', 'SPRS_ACTIVE_USER_DELIMITED',
    'SAFT11kHz8BitMono', 'SGLexicalNoSpecialChars',
    'eLEXTYPE_PRIVATE1', 'SPEI_VISEME', 'ISpGrammarBuilder',
    'SPEI_SENTENCE_BOUNDARY', 'SECFDefault',
    'DISPID_SPIAudioSizeTime',
    'SPSMF_SRGS_SEMANTICINTERPRETATION_W3C',
    'DISPID_SRSetPropertyNumber', 'SpeechRegistryUserRoot',
    'DISPID_SRCCreateResultFromMemory', 'DISPID_SDKSetBinaryValue',
    'SPWF_INPUT', 'DISPID_SGRSAddSpecialTransition',
    'DISPID_SRCCreateGrammar', 'SPAR_Unknown', 'DISPID_SVSPhonemeId',
    'SVSFNLPSpeakPunc', 'SPRST_INACTIVE_WITH_PURGE',
    'ISpeechRecognizer', 'SRTExtendableParse', 'DISPID_SLPSymbolic',
    'ISpeechCustomStream', 'SPBO_TIME_UNITS',
    'DISPID_SVESentenceBoundary', 'DISPID_SRCPause', 'SPPS_Unknown',
    'SpeechCategoryAudioIn', 'DISPID_SREmulateRecognition',
    'SSFMCreateForWrite', 'DISPID_SOTGetStorageFileName',
    'SPWP_UNKNOWN_WORD_PRONOUNCEABLE', 'eLEXTYPE_PRIVATE20',
    'SpStream', 'SPEI_RECO_STATE_CHANGE', 'SAFT11kHz8BitStereo',
    'DISPID_SPRulesCount', 'SpInProcRecoContext',
    'SPFM_OPEN_READONLY', 'DISPID_SDKGetlongValue',
    'SAFTTrueSpeech_8kHz1BitMono', 'SRADefaultToActive',
    'eLEXTYPE_PRIVATE17', 'SVSFParseSsml', 'DISPID_SLWPronunciations',
    'ISpRecognizer3', 'SPEI_RECOGNITION', 'DISPID_SRCRecognizer',
    'DISPID_SPANumberOfElementsInResult', 'SGRSTTTextBuffer',
    'SPPS_Interjection', 'DISPID_SVSInputWordPosition',
    'SGRSTTWildcard', 'SPEI_TTS_PRIVATE', 'SVP_6',
    'DISPID_SRRGetXMLResult', 'SpeechRuleAttributes',
    'DISPID_SVEventInterests', 'DISPID_SGRSTPropertyValue',
    'DISPID_SpeechAudioFormat', 'SPDKL_CurrentUser',
    'SAFT44kHz16BitStereo', 'SPEI_START_INPUT_STREAM',
    'DISPIDSPTSI_SelectionLength', 'DISPID_SVAudioOutput',
    'SPEI_ADAPTATION', 'SRTEmulated', 'SVP_16',
    'DISPID_SVSRunningState', 'DISPIDSPTSI_ActiveOffset',
    'SPEI_RESERVED5', 'SLTUser', '__MIDL_IWinTypes_0009',
    'SVESentenceBoundary', 'ISpStreamFormatConverter',
    'DISPID_SRGCmdLoadFromFile', 'SVPNormal', 'SAFTGSM610_44kHzMono',
    'DISPID_SRIsUISupported', 'DISPID_SBSFormat', 'SPPS_RESERVED1',
    'SpeechLoadOption', 'SpeechWordType', 'DISPID_SLWType',
    'DISPID_SRRTLength', 'DISPID_SpeechRecoResultTimes',
    'DISPID_SOTCSetId', 'SPRECOCONTEXTSTATUS', 'SP_VISEME_14',
    'DISPID_SPRuleId', 'ISpeechRecognizerStatus', 'SP_VISEME_18',
    'DISPID_SFSOpen', 'SGPronounciation', 'DISPID_SRGDictationLoad',
    'DISPID_SVSpeakCompleteEvent', 'SPAUDIOSTATE', 'SPLO_DYNAMIC',
    'DISPID_SRSCurrentStreamNumber', 'ISpRecoGrammar2',
    'SAFTADPCM_22kHzStereo', 'DISPID_SOTRemove',
    'Speech_StreamPos_Asap', 'SDTProperty',
    'ISpeechPhraseReplacements', 'SpeechAudioProperties',
    'SP_VISEME_21', 'DISPID_SpeechPhraseAlternates', 'SWTAdded',
    'SRAExport', 'SPRS_ACTIVE', 'DISPID_SRCESoundStart',
    'eLEXTYPE_VENDORLEXICON', 'SAFT32kHz16BitStereo',
    'SDKLCurrentUser', 'SPPHRASEELEMENT', 'ISpeechPhraseAlternate',
    'ISpeechAudio', 'SPRST_ACTIVE_ALWAYS', 'SAFTADPCM_44kHzStereo',
    'DISPID_SRRTTickCount', 'DISPID_SRGRules', 'SPBO_AHEAD',
    'UINT_PTR', 'SPRECOSTATE', 'DISPID_SPRs_NewEnum', 'SP_VISEME_13',
    'ISpeechLexiconPronunciation', 'SPEI_MIN_TTS',
    'DISPID_SOTCEnumerateTokens', 'DISPID_SLPPhoneIds',
    'DISPID_SpeechLexiconWord', 'ISpObjectWithToken',
    'DISPID_SPIEnginePrivateData', 'DISPID_SABufferInfo',
    'ISpeechAudioFormat', 'DISPID_SPAs_NewEnum',
    'SGDSActiveWithAutoPause', 'DISPID_SGRSTransitions',
    'ISpeechPhraseInfoBuilder', 'SITooFast', 'SREInterference',
    'DISPID_SVAlertBoundary', 'SBONone', 'SVSFUnusedFlags',
    'SPGRAMMARWORDTYPE', 'DISPID_SPEAudioSizeBytes',
    '_ISpeechVoiceEvents', 'SRERecognition',
    'SPEI_PROPERTY_NUM_CHANGE', 'DISPID_SVSpeak',
    'DISPID_SDKEnumKeys', 'SpSharedRecoContext', 'SPAUDIOSTATUS',
    'SVEVoiceChange', 'SVP_8', 'DISPID_SAEventHandle', 'SP_VISEME_4',
    'DISPID_SPRules_NewEnum', 'SVEStartInputStream',
    'DISPID_SRGCmdSetRuleState', 'SPXMLRESULTOPTIONS',
    'ISpeechObjectTokenCategory', 'SpeechEngineProperties',
    'DISPID_SGRSTsCount', 'SVEBookmark', 'SPSHORTCUTPAIRLIST',
    '__MIDL___MIDL_itf_sapi_0000_0020_0002', 'SVF_Stressed',
    'SAFTCCITT_uLaw_22kHzStereo', 'SPEI_WORD_BOUNDARY',
    'SpeechRecoProfileProperties', 'SPSMF_SRGS_SAPIPROPERTIES',
    'SITooQuiet', 'SPVOICESTATUS', 'DISPID_SRCEBookmark',
    'DISPID_SRGState', 'DISPID_SRGCmdLoadFromResource',
    'DISPID_SRSAudioStatus', 'DISPID_SRRRecoContext',
    'SpeechBookmarkOptions', 'DISPID_SRSClsidEngine',
    'DISPID_SPRuleEngineConfidence', 'DISPIDSPTSI', 'SGDSActive',
    'SPWT_LEXICAL_NO_SPECIAL_CHARS', 'DISPID_SLWWord',
    'DISPID_SPISaveToMemory', 'SVP_12', 'SAFT11kHz16BitStereo',
    'DISPID_SPIGrammarId', 'SGRSTTDictation', 'ISpeechDataKey',
    'SpeechRecoEvents', 'ISpeechVoiceStatus', 'SPPS_SuppressWord',
    'DISPID_SRCResume', 'DISPID_SGRSTText',
    'DISPID_SpeechPhraseBuilder', 'SAFT22kHz8BitMono',
    'SpAudioFormat', 'DISPID_SPRuleChildren', 'DISPID_SLPLangId',
    'SpVoice', 'DISPID_SVEWord', 'SPFM_CREATE_ALWAYS',
    'DISPID_SPEAudioSizeTime', 'SPCT_SUB_DICTATION',
    'DISPID_SASNonBlockingIO', 'SPEI_MAX_TTS',
    'SpeechTokenIdUserLexicon', 'STCLocalServer',
    'DISPID_SpeechMMSysAudio', 'SAFTADPCM_22kHzMono',
    'DISPID_SRCEFalseRecognition', 'DISPID_SRCEHypothesis',
    'DISPID_SRGCmdSetRuleIdState', 'SPPHRASEREPLACEMENT', 'SVP_17',
    'SPEI_MAX_SR', 'SAFT12kHz16BitStereo', 'SPSVerb',
    'eLEXTYPE_RESERVED10', 'DISPID_SPERequiredConfidence',
    'SPVPRI_ALERT', 'SPSEMANTICERRORINFO', 'SAFT24kHz16BitMono',
    'DISPID_SpeechDataKey', 'SPXRO_Alternates_SML', 'SPWT_LEXICAL',
    'DISPID_SLWsItem', 'SP_VISEME_12', 'ISpeechGrammarRules',
    'SPEI_SR_RETAINEDAUDIO', 'DISPID_SpeechLexiconPronunciation',
    'SAFT22kHz16BitMono', 'SAFTADPCM_44kHzMono',
    'DISPID_SRGIsPronounceable', 'SWPUnknownWordPronounceable',
    'DISPID_SPAsItem', 'DISPID_SRRTStreamTime', 'SPPROPERTYINFO',
    'DISPID_SRCState', 'eLEXTYPE_PRIVATE7', 'SDTRule',
    'DISPID_SOTDataKey', 'DISPID_SASState', 'SPRST_ACTIVE',
    'tagSPPROPERTYINFO', 'SRSActiveAlways',
    'SAFTCCITT_uLaw_11kHzStereo', 'DISPID_SRSNumberOfActiveRules',
    'SPEI_END_SR_STREAM', 'SAFT8kHz16BitStereo',
    'ISpeechGrammarRuleStateTransitions', 'ISpRecoContext2',
    'SVSFlagsAsync', 'SDTDisplayText', 'ISpeechPhoneConverter',
    'SRAORetainAudio', 'DISPID_SVEBookmark',
    'SpeechAudioFormatGUIDWave', 'SpeechLexiconType',
    'DISPID_SpeechRecognizerStatus', 'DISPID_SOTId',
    'SDKLCurrentConfig', 'DISPID_SpeechRecognizer', 'SGSExclusive',
    'SAFT32kHz16BitMono', 'SGDSActiveUserDelimited',
    'ISpeechBaseStream', 'SpeechCategoryRecognizers',
    'SpeechVoicePriority', 'SDA_Two_Trailing_Spaces',
    'DISPID_SLAddPronunciation', 'DISPID_SVSyncronousSpeakTimeout',
    'SPBOOKMARKOPTIONS', 'SVP_7', 'eLEXTYPE_PRIVATE12',
    'SpeechAudioFormatGUIDText', 'DISPID_SWFEBitsPerSample',
    'DISPID_SVEEnginePrivate', 'DISPID_SPRuleName',
    'SpUnCompressedLexicon', 'SPEI_RESERVED2',
    'SpeechEngineConfidence', 'DISPID_SGRsDynamic', 'SVSFVoiceMask',
    'SVP_21', 'DISPID_SVGetAudioOutputs', 'SRATopLevel',
    'DISPID_SRDisplayUI', 'SPWP_KNOWN_WORD_PRONOUNCEABLE',
    'DISPID_SWFESamplesPerSec', 'ISpeechPhraseRule',
    'DISPID_SVWaitUntilDone', 'DISPID_SLPs_NewEnum', 'SPPS_Noun',
    'DISPID_SLWs_NewEnum', 'ISpMMSysAudio', 'SGRSTTRule', 'SVP_20',
    'SpeechPropertyComplexResponseSpeed', 'DISPID_SVSVisemeId',
    'ISpeechGrammarRuleStateTransition', 'DISPID_SVEStreamEnd',
    'DISPID_SpeechPhraseReplacement', 'DISPID_SpeechVoiceEvent',
    'SpeechPropertyAdaptationOn', 'SP_VISEME_0', 'SPCT_SLEEP',
    'SVSFIsFilename', 'DISPID_SRRecognizer', 'SRAONone',
    'SpeechVoiceCategoryTTSRate', 'DISPID_SRGCommit',
    'SPADAPTATIONRELEVANCE', 'DISPID_SVPriority', 'DISPID_SAStatus',
    'DISPID_SLGetPronunciations', 'SPEI_END_INPUT_STREAM',
    'SpeechRegistryLocalMachineRoot', 'ISpeechPhraseProperties',
    'DISPID_SVGetProfiles', 'SPSSuppressWord', 'SPSHORTCUTPAIR',
    'SPSTREAMFORMATTYPE', 'DISPID_SOTSetId', 'DISPID_SPIElements',
    'eLEXTYPE_PRIVATE6', 'ISpSerializeState',
    'SSSPTRelativeToCurrentPosition', 'SAFTGSM610_8kHzMono',
    'SpeechTokenKeyUI', 'SPINTERFERENCE_LATENCY_TRUNCATE_END',
    'SAFT8kHz8BitStereo', 'STSF_LocalAppData', 'SpMMAudioOut',
    'SpeechRecognizerState', 'DISPID_SGRAddState',
    'SpeechVisemeFeature', 'DISPID_SLWLangId', 'ISpeechXMLRecoResult',
    'ISpAudio', 'DISPID_SRAllowAudioInputFormatChangesOnNextSet',
    'DISPID_SVSInputWordLength', 'SVP_18', 'DISPID_SRGReset',
    'SPDKL_LocalMachine', 'eLEXTYPE_PRIVATE15', 'ISpRecoCategory',
    'eLEXTYPE_PRIVATE3', 'SpeechRetainedAudioOptions',
    'ISpeechLexiconWord', 'Speech_Max_Pron_Length',
    'DISPID_SDKEnumValues', 'eLEXTYPE_USER_SHORTCUT',
    'DISPID_SRStatus', 'ISpeechLexicon', 'SAFT16kHz8BitMono',
    'DISPID_SRGetRecognizers', 'eLEXTYPE_LETTERTOSOUND',
    'SpMemoryStream', 'SPEI_PHONEME', 'SP_VISEME_5',
    'DISPID_SDKOpenKey', 'SVSFPurgeBeforeSpeak',
    'DISPID_SVSLastStreamNumberQueued', 'SRAInterpreter',
    'ISpDataKey', 'SITooLoud', 'ISpRecognizer2',
    'DISPID_SpeechAudioBufferInfo', 'SPAS_PAUSE',
    'DISPID_SPPNumberOfElements', 'SAFTGSM610_22kHzMono',
    'DISPID_SpeechObjectTokens', 'DISPID_SVPause',
    'SPEI_PROPERTY_STRING_CHANGE', 'DISPID_SOTIsUISupported',
    'DISPID_SPILanguageId', 'SPRST_INACTIVE', 'DISPID_SOTCId',
    'DISPID_SpeechObjectTokenCategory', 'DISPID_SGRsCommitAndSave',
    'SREAdaptation', 'SAFTADPCM_11kHzMono', 'DISPID_SABIEventBias',
    'SVP_4', 'SpeechAudioVolume', 'SPINTERFERENCE',
    'DISPID_SpeechVoice', 'eLEXTYPE_PRIVATE18',
    'DISPID_SPRuleConfidence', 'tagSPTEXTSELECTIONINFO',
    'SAFTADPCM_8kHzMono', 'SREStateChange', 'DISPID_SWFEExtraData',
    'DISPID_SPEAudioStreamOffset', 'SPDKL_DefaultLocation',
    'DISPID_SPRNumberOfElements', 'DISPID_SBSRead', 'IStream',
    'DISPID_SCSBaseStream', 'DISPID_SRRAudioFormat', 'SGRSTTEpsilon',
    'SPCT_COMMAND', '__MIDL___MIDL_itf_sapi_0000_0020_0001',
    'SPCATEGORYTYPE', 'DISPID_SPELexicalForm', 'SPAS_RUN',
    'SPINTERFERENCE_TOOSLOW', 'SAFT16kHz8BitStereo',
    'SpeechTokenShellFolder', 'DISPID_SPIRule',
    'DISPID_SpeechPhraseElements', 'SPEI_MIN_SR',
    'DISPID_SLRemovePronunciation', 'SDTReplacement',
    'SpPhraseInfoBuilder', 'DISPID_SWFEFormatTag',
    'SREFalseRecognition', 'DISPID_SLAddPronunciationByPhoneIds',
    'ISpeechVoice', 'eLEXTYPE_RESERVED7',
    'DISPID_SRCRetainedAudioFormat', 'DISPID_SGRSTRule',
    'DISPID_SRCCmdMaxAlternates', 'SP_VISEME_10',
    'SpeechTokenContext', 'SPCONTEXTSTATE',
    'DISPID_SWFEAvgBytesPerSec', 'SRSActive', 'SpObjectTokenCategory',
    'DISPID_SPRDisplayAttributes', 'SpeechDiscardType',
    'DISPID_SRCSetAdaptationData', 'DISPID_SPRulesItem',
    'DISPID_SVEAudioLevel', 'DISPID_SPPsCount',
    'SPSMF_SAPI_PROPERTIES', 'SPINTERFERENCE_TOOFAST',
    'DISPID_SGRsCount', 'DISPID_SVAudioOutputStream',
    'DISPID_SRAllowVoiceFormatMatchingOnNextSet',
    'DISPID_SABufferNotifySize', 'DISPID_SGRSTPropertyName',
    'DISPID_SRGetFormat', 'eLEXTYPE_PRIVATE16', 'SPRULESTATE',
    'DISPID_SpeechPhraseInfo', 'SAFTCCITT_uLaw_44kHzMono',
    'DISPID_SVEViseme', 'DISPID_SpeechXMLRecoResult',
    'SPAO_RETAIN_AUDIO', 'DISPID_SRCBookmark', 'SVP_11',
    'STSF_CommonAppData', 'SpeechVisemeType',
    'SPSMF_SRGS_SEMANTICINTERPRETATION_MS', 'SRSInactiveWithPurge',
    'DISPID_SpeechRecoContext', 'DISPID_SLGetGenerationChange',
    'SpResourceManager', 'SRCS_Enabled', 'ISpeechRecoGrammar',
    'SPPHRASEPROPERTY', 'SAFTCCITT_uLaw_8kHzMono', 'SPGS_DISABLED',
    'SGDisplay', 'SPPARTOFSPEECH', 'SPVPRI_NORMAL',
    'SpeechPropertyLowConfidenceThreshold', 'DISPID_SPRText',
    'ISpNotifySource', 'SpeechPropertyNormalConfidenceThreshold',
    'ISpeechFileStream', 'SAFTCCITT_ALaw_11kHzStereo',
    'DISPID_SPPsItem', 'SPEI_SOUND_START', 'SAFT16kHz16BitStereo',
    'SAFTCCITT_uLaw_44kHzStereo', 'IInternetSecurityManager',
    'DISPID_SRGetPropertyNumber', 'SPLEXICONTYPE',
    'DISPID_SMSALineId', 'SVEWordBoundary',
    'DISPID_SOTRemoveStorageFileName', 'SPBO_NONE', 'SBOPause',
    'SSSPTRelativeToStart', 'DISPIDSPTSI_ActiveLength', 'SLOStatic',
    'SpMMAudioIn', 'eLEXTYPE_PRIVATE2', 'SVP_1',
    'SPEI_START_SR_STREAM', 'DISPID_SAFType', 'SPBINARYGRAMMAR',
    'SpeechVoiceEvents', 'SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE',
    'SPEI_UNDEFINED', 'DISPID_SBSWrite', 'eLEXTYPE_PRIVATE5',
    'SPXRO_SML', 'DISPID_SDKCreateKey', 'ISpeechResourceLoader',
    'ISpeechLexiconWords', 'SpShortcut'
]

