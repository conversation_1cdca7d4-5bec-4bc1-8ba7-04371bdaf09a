from enum import IntFlag

import comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 as __wrapper_module__
from comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 import (
    FON<PERSON><PERSON>IKETHROUGH, Co<PERSON>lass, <PERSON><PERSON>PPARAMS, OLE_YSIZE_HIMETRIC,
    <PERSON>LE_ENABLEDEFAULTBOOL, BSTR, FONTITALIC, IFontDisp, typelib_path,
    Checked, OLE_OPTEXCLUSIVE, EXCEPINFO, VARIANT_BOOL, dispid,
    VgaColor, DISPMETHOD, GUID, OLE_YSIZE_PIXELS, StdPicture, Color,
    IFont, StdFont, IEnumVARIANT, OLE_XPOS_HIMETRIC, Picture,
    IPicture, OLE_XSIZE_CONTAINER, OLE_YPOS_PIXELS, IFontEventsDisp,
    Monochrome, OLE_YPOS_HIMETRIC, FON<PERSON>BO<PERSON>, Gray, DISPPROPERTY,
    OLE_YSIZE_CONTAINER, <PERSON>LE_XPOS_PIXELS, IPictureDisp,
    <PERSON>LE_CANCELBOOL, COMMETHOD, OLE_XPOS_CONTAINER, OLE_XSIZE_HIMETRIC,
    FONTUNDERSCORE, Font, OLE_YPOS_CONTAINER, Unchecked, OLE_COLOR,
    FontEvents, FONTNAME, OLE_HANDLE, OLE_XSIZE_PIXELS, Default,
    HRESULT, _check_version, _lcid, IUnknown, FONTSIZE, IDispatch,
    Library
)


class OLE_TRISTATE(IntFlag):
    Unchecked = 0
    Checked = 1
    Gray = 2


class LoadPictureConstants(IntFlag):
    Default = 0
    Monochrome = 1
    VgaColor = 2
    Color = 4


__all__ = [
    'FONTSTRIKETHROUGH', 'OLE_XPOS_PIXELS', 'OLE_YSIZE_HIMETRIC',
    'OLE_ENABLEDEFAULTBOOL', 'OLE_CANCELBOOL', 'FONTITALIC',
    'IFontDisp', 'typelib_path', 'Checked', 'OLE_TRISTATE',
    'FONTSIZE', 'OLE_XPOS_CONTAINER', 'OLE_XSIZE_HIMETRIC',
    'OLE_OPTEXCLUSIVE', 'FONTUNDERSCORE', 'Font',
    'LoadPictureConstants', 'OLE_YPOS_CONTAINER', 'Unchecked',
    'OLE_COLOR', 'FontEvents', 'FONTBOLD', 'VgaColor', 'FONTNAME',
    'OLE_HANDLE', 'OLE_XSIZE_PIXELS', 'OLE_YSIZE_PIXELS',
    'StdPicture', 'Color', 'Default', 'IFont', 'StdFont',
    'OLE_XPOS_HIMETRIC', 'Picture', 'IPicture', 'OLE_XSIZE_CONTAINER',
    'OLE_YPOS_PIXELS', 'IFontEventsDisp', 'Monochrome',
    'OLE_YPOS_HIMETRIC', 'Gray', 'OLE_YSIZE_CONTAINER',
    'IPictureDisp', 'Library'
]

